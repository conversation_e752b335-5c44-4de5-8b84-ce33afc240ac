name: "Continuous Integration"

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch:

jobs:
  ci:
    name: "🚀 CI"
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - name: 📚 Git Checkout
        uses: actions/checkout@v4

      - name: 🐦 Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: 3.32.0
          channel: stable
          cache: true

      - name: 📦 Install Dependencies
        run: |
          flutter pub global activate very_good_cli
          flutter pub global activate coverage

      - name: 🦄 Bootstrap
        run: bash ./scripts/bootstrap.bash

      - name: Install DCM
        uses: CQLabs/setup-dcm@v2
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}

      - name: ✨ Check Formatting
        run: bash ./scripts/format.bash

      - name: 🕵️ Analyze
        run: bash ./scripts/analyze.bash

      # Remove this step if you don't use Dart Code Metrics
      - name: 🕵️ Analyze with DCM
        run: dcm analyze . -r github

      # ———————————————————————————————————————————————————————
      #   RUN TESTS + COVERAGE
      # ———————————————————————————————————————————————————————

      - name: 🧪 Run Tests
        run: bash ./scripts/test.bash

      - name: 📥 Upload test report
        uses: actions/upload-artifact@v4
        if: (success() || failure()) && ${{ github.actor != 'dependabot[bot]' }}
        with:
          name: test-results
          path: reports/tests.json

  # ———————————————————————————————————————————————————————
  #   TEST REPORT JOB
  # ———————————————————————————————————————————————————————
  report_tests:
    name: "🚛 Test report"
    runs-on: ubuntu-latest
    timeout-minutes: 10
    # continue-on-error allows this job to run even if 'ci' fails
    continue-on-error: true
    needs: [ci]
    steps:
      - name: Test Report
        uses: dorny/test-reporter@v1
        with:
          artifact: test-results
          name: Test Report
          path: "**/tests.json"
          reporter: flutter-json
          fail-on-error: false
