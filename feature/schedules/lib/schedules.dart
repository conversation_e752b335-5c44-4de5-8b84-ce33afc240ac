/// Schedules feature library
/// 
/// This library exports all the public APIs of the schedules feature.
/// Other parts of the app should only import this file, not internal files.
library schedules;

// Domain exports
export 'src/domain/models/class_schedule.dart';
export 'src/domain/repositories/schedules_repository.dart';

// Data exports
export 'src/data/repositories/schedules_repository_impl.dart';
export 'src/data/datasources/schedules_api_datasource.dart';

// Presentation exports
export 'src/presentation/bloc/schedules_bloc.dart';
export 'src/presentation/bloc/schedules_event.dart';
export 'src/presentation/bloc/schedules_state.dart';
export 'src/presentation/widgets/schedules_screen.dart';
export 'src/presentation/widgets/calendar_widget.dart';
export 'src/presentation/widgets/schedule_card.dart';

// Injection
export 'src/injection.dart';
