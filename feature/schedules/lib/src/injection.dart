import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rest_client/rest_client.dart';
import 'data/datasources/schedules_api_datasource.dart';
import 'data/repositories/schedules_repository_impl.dart';
import 'domain/repositories/schedules_repository.dart';
import 'presentation/bloc/schedules_bloc.dart';

/// Dependency injection for schedules feature - API ONLY
class SchedulesInjection {
  /// Create schedules API data source
  static SchedulesApiDataSource createApiDataSource() {
    final restClient = RestClientHttp(baseUrl: 'http://10.0.2.2:3000');
    return SchedulesApiDataSource(restClient: restClient);
  }

  /// Create schedules repository - API ONLY
  static SchedulesRepository createRepository() {
    final apiDataSource = createApiDataSource();
    return SchedulesRepositoryImpl(
      apiDataSource: apiDataSource,
    );
  }

  /// Create schedules bloc
  static SchedulesBloc createBloc() {
    final repository = createRepository();
    return SchedulesBloc(repository: repository);
  }
}

/// Schedules scope widget that provides schedules dependencies
class SchedulesScope extends StatelessWidget {
  const SchedulesScope({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SchedulesInjection.createBloc(),
      child: child,
    );
  }

  /// Get schedules bloc from context
  static SchedulesBloc blocOf(BuildContext context) {
    return context.read<SchedulesBloc>();
  }

  /// Watch schedules bloc from context
  static SchedulesBloc watchBlocOf(BuildContext context) {
    return context.watch<SchedulesBloc>();
  }
}
