import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/repositories/schedules_repository.dart';
import '../../domain/models/class_schedule.dart';
import 'schedules_event.dart';
import 'schedules_state.dart';

/// Bloc for managing schedules state and business logic
class SchedulesBloc extends Bloc<SchedulesEvent, SchedulesState> {
  SchedulesBloc({
    required SchedulesRepository repository,
  })  : _repository = repository,
        super(const SchedulesState()) {
    on<SchedulesLoadRequested>(_onLoadRequested);
    on<SchedulesRefreshRequested>(_onRefreshRequested);
    on<SchedulesDateChanged>(_onDateChanged);
    on<TodaySchedulesRequested>(_onTodaySchedulesRequested);
    on<SchedulesSearchRequested>(_onSearchRequested);
    on<SchedulesSearchCleared>(_onSearchCleared);
    on<ScheduleBookingRequested>(_onBookingRequested);
    on<ScheduleBookingCancelled>(_onBookingCancelled);
    on<UpcomingSchedulesRequested>(_onUpcomingSchedulesRequested);
  }

  final SchedulesRepository _repository;

  /// Handle schedules load request
  Future<void> _onLoadRequested(
    SchedulesLoadRequested event,
    Emitter<SchedulesState> emit,
  ) async {
    emit(state.copyWith(status: SchedulesStatus.loading));

    try {
      // Load today's schedules and upcoming schedules concurrently
      final results = await Future.wait([
        _repository.getTodaySchedules(tenantId: event.tenantId),
        _repository.getUpcomingSchedules(tenantId: event.tenantId, limit: 10),
        _repository.getClassSchedules(tenantId: event.tenantId),
      ]);

      emit(state.copyWith(
        status: SchedulesStatus.success,
        todaySchedules: results[0] as List<ClassSchedule>,
        upcomingSchedules: results[1] as List<ClassSchedule>,
        schedules: results[2] as List<ClassSchedule>,
        errorMessage: null,
      ));
    } catch (error) {
      emit(state.copyWith(
        status: SchedulesStatus.failure,
        errorMessage: error.toString(),
      ));
    }
  }

  /// Handle schedules refresh request
  Future<void> _onRefreshRequested(
    SchedulesRefreshRequested event,
    Emitter<SchedulesState> emit,
  ) async {
    emit(state.copyWith(isRefreshing: true));

    try {
      final results = await Future.wait([
        _repository.getTodaySchedules(tenantId: event.tenantId),
        _repository.getUpcomingSchedules(tenantId: event.tenantId, limit: 10),
        _repository.getClassSchedules(tenantId: event.tenantId),
      ]);

      emit(state.copyWith(
        status: SchedulesStatus.success,
        todaySchedules: results[0] as List<ClassSchedule>,
        upcomingSchedules: results[1] as List<ClassSchedule>,
        schedules: results[2] as List<ClassSchedule>,
        isRefreshing: false,
        errorMessage: null,
      ));
    } catch (error) {
      emit(state.copyWith(
        isRefreshing: false,
        errorMessage: error.toString(),
      ));
    }
  }

  /// Handle date change
  Future<void> _onDateChanged(
    SchedulesDateChanged event,
    Emitter<SchedulesState> emit,
  ) async {
    emit(state.copyWith(selectedDate: event.date));

    try {
      final schedules = await _repository.getSchedulesForDate(
        tenantId: event.tenantId,
        date: event.date,
      );

      // Update the schedules list with the new data
      emit(state.copyWith(
        schedules: schedules,
        status: SchedulesStatus.success,
      ));
    } catch (error) {
      emit(state.copyWith(
        errorMessage: error.toString(),
      ));
    }
  }

  /// Handle today schedules request
  Future<void> _onTodaySchedulesRequested(
    TodaySchedulesRequested event,
    Emitter<SchedulesState> emit,
  ) async {
    emit(state.copyWithClearedDate());

    try {
      final todaySchedules = await _repository.getTodaySchedules(
        tenantId: event.tenantId,
      );

      emit(state.copyWith(
        todaySchedules: todaySchedules,
        status: SchedulesStatus.success,
      ));
    } catch (error) {
      emit(state.copyWith(
        errorMessage: error.toString(),
      ));
    }
  }

  /// Handle search request
  Future<void> _onSearchRequested(
    SchedulesSearchRequested event,
    Emitter<SchedulesState> emit,
  ) async {
    emit(state.copyWith(searchQuery: event.query));

    if (event.query.isEmpty) {
      emit(state.copyWith(searchResults: []));
      return;
    }

    try {
      final searchResults = await _repository.searchSchedules(
        tenantId: event.tenantId,
        query: event.query,
      );

      emit(state.copyWith(
        searchResults: searchResults,
        status: SchedulesStatus.success,
      ));
    } catch (error) {
      emit(state.copyWith(
        errorMessage: error.toString(),
      ));
    }
  }

  /// Handle search clear
  Future<void> _onSearchCleared(
    SchedulesSearchCleared event,
    Emitter<SchedulesState> emit,
  ) async {
    emit(state.copyWithClearedSearch());
  }

  /// Handle booking request
  Future<void> _onBookingRequested(
    ScheduleBookingRequested event,
    Emitter<SchedulesState> emit,
  ) async {
    emit(state.copyWith(
      isBooking: true,
      bookingScheduleId: event.scheduleId,
    ));

    try {
      await _repository.bookSchedule(
        scheduleId: event.scheduleId,
        tenantId: event.tenantId,
      );

      // Refresh the schedules to get updated booking counts
      add(SchedulesRefreshRequested(tenantId: event.tenantId));

      emit(state.copyWith(
        isBooking: false,
        bookingScheduleId: null,
      ));
    } catch (error) {
      emit(state.copyWith(
        isBooking: false,
        bookingScheduleId: null,
        errorMessage: error.toString(),
      ));
    }
  }

  /// Handle booking cancellation
  Future<void> _onBookingCancelled(
    ScheduleBookingCancelled event,
    Emitter<SchedulesState> emit,
  ) async {
    emit(state.copyWith(
      isBooking: true,
      bookingScheduleId: event.scheduleId,
    ));

    try {
      await _repository.cancelBooking(
        scheduleId: event.scheduleId,
        tenantId: event.tenantId,
      );

      // Refresh the schedules to get updated booking counts
      add(SchedulesRefreshRequested(tenantId: event.tenantId));

      emit(state.copyWith(
        isBooking: false,
        bookingScheduleId: null,
      ));
    } catch (error) {
      emit(state.copyWith(
        isBooking: false,
        bookingScheduleId: null,
        errorMessage: error.toString(),
      ));
    }
  }

  /// Handle upcoming schedules request
  Future<void> _onUpcomingSchedulesRequested(
    UpcomingSchedulesRequested event,
    Emitter<SchedulesState> emit,
  ) async {
    try {
      final upcomingSchedules = await _repository.getUpcomingSchedules(
        tenantId: event.tenantId,
        limit: event.limit,
      );

      emit(state.copyWith(
        upcomingSchedules: upcomingSchedules,
        status: SchedulesStatus.success,
      ));
    } catch (error) {
      emit(state.copyWith(
        errorMessage: error.toString(),
      ));
    }
  }
}
