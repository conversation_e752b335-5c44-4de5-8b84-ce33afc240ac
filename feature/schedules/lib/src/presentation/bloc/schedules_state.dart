import 'package:equatable/equatable.dart';
import '../../domain/models/class_schedule.dart';

/// Schedules state status enum
enum SchedulesStatus {
  initial,
  loading,
  success,
  failure,
}

/// Schedules state class
class SchedulesState extends Equatable {
  const SchedulesState({
    this.status = SchedulesStatus.initial,
    this.schedules = const [],
    this.todaySchedules = const [],
    this.upcomingSchedules = const [],
    this.searchResults = const [],
    this.selectedDate,
    this.searchQuery = '',
    this.errorMessage,
    this.isRefreshing = false,
    this.isBooking = false,
    this.bookingScheduleId,
  });

  /// Current status of the schedules
  final SchedulesStatus status;

  /// All schedules
  final List<ClassSchedule> schedules;

  /// Today's schedules
  final List<ClassSchedule> todaySchedules;

  /// Upcoming schedules
  final List<ClassSchedule> upcomingSchedules;

  /// Search results
  final List<ClassSchedule> searchResults;

  /// Currently selected date
  final DateTime? selectedDate;

  /// Current search query
  final String searchQuery;

  /// Error message if any
  final String? errorMessage;

  /// Whether data is being refreshed
  final bool isRefreshing;

  /// Whether a booking operation is in progress
  final bool isBooking;

  /// ID of schedule being booked/cancelled
  final String? bookingScheduleId;

  /// Whether the schedules are in loading state
  bool get isLoading => status == SchedulesStatus.loading;

  /// Whether the schedules have data
  bool get hasData => status == SchedulesStatus.success;

  /// Whether the schedules have an error
  bool get hasError => status == SchedulesStatus.failure;

  /// Whether search is active
  bool get isSearchActive => searchQuery.isNotEmpty;

  /// Get schedules to display based on current state
  List<ClassSchedule> get displaySchedules {
    if (isSearchActive) {
      return searchResults;
    }
    
    if (selectedDate != null) {
      // Filter schedules for selected date
      return schedules.where((schedule) {
        final scheduleDate = schedule.startTime;
        return scheduleDate.year == selectedDate!.year &&
               scheduleDate.month == selectedDate!.month &&
               scheduleDate.day == selectedDate!.day;
      }).toList();
    }
    
    return todaySchedules;
  }

  /// Get schedules count for display
  int get displaySchedulesCount => displaySchedules.length;

  /// Get display title based on current state
  String get displayTitle {
    if (isSearchActive) {
      return 'Search Results';
    }
    
    if (selectedDate != null) {
      final now = DateTime.now();
      final selected = selectedDate!;
      
      if (selected.year == now.year &&
          selected.month == now.month &&
          selected.day == now.day) {
        return "Today's Schedule";
      }
      
      return 'Schedule for ${selected.day}/${selected.month}';
    }
    
    return "Today's Schedule";
  }

  /// Get subtitle for display
  String get displaySubtitle {
    final count = displaySchedulesCount;
    if (count == 0) {
      return 'No classes scheduled';
    } else if (count == 1) {
      return '1 class';
    } else {
      return '$count classes';
    }
  }

  /// Create a copy of this state with updated values
  SchedulesState copyWith({
    SchedulesStatus? status,
    List<ClassSchedule>? schedules,
    List<ClassSchedule>? todaySchedules,
    List<ClassSchedule>? upcomingSchedules,
    List<ClassSchedule>? searchResults,
    DateTime? selectedDate,
    String? searchQuery,
    String? errorMessage,
    bool? isRefreshing,
    bool? isBooking,
    String? bookingScheduleId,
  }) {
    return SchedulesState(
      status: status ?? this.status,
      schedules: schedules ?? this.schedules,
      todaySchedules: todaySchedules ?? this.todaySchedules,
      upcomingSchedules: upcomingSchedules ?? this.upcomingSchedules,
      searchResults: searchResults ?? this.searchResults,
      selectedDate: selectedDate ?? this.selectedDate,
      searchQuery: searchQuery ?? this.searchQuery,
      errorMessage: errorMessage ?? this.errorMessage,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      isBooking: isBooking ?? this.isBooking,
      bookingScheduleId: bookingScheduleId ?? this.bookingScheduleId,
    );
  }

  /// Create a copy with cleared search
  SchedulesState copyWithClearedSearch() {
    return copyWith(
      searchQuery: '',
      searchResults: [],
    );
  }

  /// Create a copy with cleared selected date
  SchedulesState copyWithClearedDate() {
    return copyWith(
      selectedDate: null,
    );
  }

  @override
  List<Object?> get props => [
        status,
        schedules,
        todaySchedules,
        upcomingSchedules,
        searchResults,
        selectedDate,
        searchQuery,
        errorMessage,
        isRefreshing,
        isBooking,
        bookingScheduleId,
      ];

  @override
  String toString() => 'SchedulesState(status: $status, schedules: ${schedules.length}, '
      'todaySchedules: ${todaySchedules.length}, searchQuery: $searchQuery)';
}
