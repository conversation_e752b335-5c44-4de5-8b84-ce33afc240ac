import 'package:equatable/equatable.dart';

/// Base class for all schedules events
sealed class SchedulesEvent extends Equatable {
  const SchedulesEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load schedules data
class SchedulesLoadRequested extends SchedulesEvent {
  const SchedulesLoadRequested({
    this.tenantId = 1,
  });

  final int tenantId;

  @override
  List<Object?> get props => [tenantId];
}

/// Event to refresh schedules data
class SchedulesRefreshRequested extends SchedulesEvent {
  const SchedulesRefreshRequested({
    this.tenantId = 1,
  });

  final int tenantId;

  @override
  List<Object?> get props => [tenantId];
}

/// Event to load schedules for a specific date
class SchedulesDateChanged extends SchedulesEvent {
  const SchedulesDateChanged({
    required this.date,
    this.tenantId = 1,
  });

  final DateTime date;
  final int tenantId;

  @override
  List<Object?> get props => [date, tenantId];
}

/// Event to load today's schedules
class TodaySchedulesRequested extends SchedulesEvent {
  const TodaySchedulesRequested({
    this.tenantId = 1,
  });

  final int tenantId;

  @override
  List<Object?> get props => [tenantId];
}

/// Event to search schedules
class SchedulesSearchRequested extends SchedulesEvent {
  const SchedulesSearchRequested({
    required this.query,
    this.tenantId = 1,
  });

  final String query;
  final int tenantId;

  @override
  List<Object?> get props => [query, tenantId];
}

/// Event to clear search
class SchedulesSearchCleared extends SchedulesEvent {
  const SchedulesSearchCleared();
}

/// Event to book a schedule
class ScheduleBookingRequested extends SchedulesEvent {
  const ScheduleBookingRequested({
    required this.scheduleId,
    this.tenantId = 1,
  });

  final String scheduleId;
  final int tenantId;

  @override
  List<Object?> get props => [scheduleId, tenantId];
}

/// Event to cancel a booking
class ScheduleBookingCancelled extends SchedulesEvent {
  const ScheduleBookingCancelled({
    required this.scheduleId,
    this.tenantId = 1,
  });

  final String scheduleId;
  final int tenantId;

  @override
  List<Object?> get props => [scheduleId, tenantId];
}

/// Event to load upcoming schedules
class UpcomingSchedulesRequested extends SchedulesEvent {
  const UpcomingSchedulesRequested({
    this.tenantId = 1,
    this.limit,
  });

  final int tenantId;
  final int? limit;

  @override
  List<Object?> get props => [tenantId, limit];
}
