import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/schedules_bloc.dart';
import '../bloc/schedules_event.dart';
import '../bloc/schedules_state.dart';
import 'calendar_widget.dart';
import 'schedule_card.dart';
import '../../domain/models/class_schedule.dart';

/// Main schedules screen widget - API ONLY
class SchedulesScreen extends StatefulWidget {
  const SchedulesScreen({super.key});

  @override
  State<SchedulesScreen> createState() => _SchedulesScreenState();
}

class _SchedulesScreenState extends State<SchedulesScreen> {
  @override
  void initState() {
    super.initState();
    // Load schedules data when screen initializes
    context.read<SchedulesBloc>().add(const SchedulesLoadRequested());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white, // White background as requested
      body: SafeArea(
        child: BlocConsumer<SchedulesBloc, SchedulesState>(
          listener: (context, state) {
            // Show error messages
            if (state.hasError && state.errorMessage != null) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('API Error: ${state.errorMessage!}'),
                  backgroundColor: Theme.of(context).colorScheme.error,
                  action: SnackBarAction(
                    label: 'Retry',
                    onPressed: () => context.read<SchedulesBloc>().add(
                      const SchedulesLoadRequested(),
                    ),
                  ),
                ),
              );
            }
          },
          builder: (context, state) {
            if (state.isLoading) {
              return const _LoadingView();
            }

            return RefreshIndicator(
              onRefresh: () async {
                context.read<SchedulesBloc>().add(const SchedulesRefreshRequested());
              },
              child: _SchedulesContent(state: state),
            );
          },
        ),
      ),
    );
  }
}

/// Loading view widget
class _LoadingView extends StatelessWidget {
  const _LoadingView();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Loading schedules from API...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.black54,
            ),
          ),
        ],
      ),
    );
  }
}

/// Main schedules content
class _SchedulesContent extends StatelessWidget {
  const _SchedulesContent({required this.state});

  final SchedulesState state;

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: [
        // Calendar section
        SliverToBoxAdapter(
          child: CalendarWidget(
            selectedDate: state.selectedDate ?? DateTime.now(),
            onDateSelected: (date) {
              context.read<SchedulesBloc>().add(
                SchedulesDateChanged(date: date),
              );
            },
          ),
        ),

        // Schedule title and count
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(20, 32, 20, 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      state.displayTitle,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    
                    const Spacer(),
                    
                    // API status indicator
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: state.hasError ? Colors.red.withOpacity(0.1) : Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        state.hasError ? 'API Error' : 'Live API',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: state.hasError ? Colors.red[700] : Colors.green[700],
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 4),
                
                Text(
                  state.displaySubtitle,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black54,
                  ),
                ),
              ],
            ),
          ),
        ),

        // Schedule cards or empty state
        if (state.displaySchedules.isEmpty)
          SliverToBoxAdapter(
            child: _EmptySchedulesView(
              hasError: state.hasError,
              errorMessage: state.errorMessage,
              onRetry: () => context.read<SchedulesBloc>().add(
                const SchedulesLoadRequested(),
              ),
            ),
          )
        else
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final schedule = state.displaySchedules[index];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: ScheduleCard(
                      schedule: schedule,
                      onTap: () => _showScheduleDetails(context, schedule),
                      onBookTap: () => _bookSchedule(context, schedule),
                      isBooking: state.isBooking && state.bookingScheduleId == schedule.id,
                    ),
                  );
                },
                childCount: state.displaySchedules.length,
              ),
            ),
          ),

        // Add to calendar button (if today is selected)
        if (state.selectedDate != null && _isToday(state.selectedDate!))
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: _AddToCalendarButton(
                date: state.selectedDate!,
                onTap: () => _addToCalendar(context, state.selectedDate!),
              ),
            ),
          ),

        // Bottom padding for navigation
        const SliverToBoxAdapter(child: SizedBox(height: 100)),
      ],
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
           date.month == now.month &&
           date.day == now.day;
  }

  void _showScheduleDetails(BuildContext context, ClassSchedule schedule) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('${schedule.displayName} details - API ID: ${schedule.id}')),
    );
  }

  void _bookSchedule(BuildContext context, ClassSchedule schedule) {
    context.read<SchedulesBloc>().add(
      ScheduleBookingRequested(scheduleId: schedule.id),
    );
  }

  void _addToCalendar(BuildContext context, DateTime date) {
    final formattedDate = '${date.day}/${date.month}';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Add to $formattedDate functionality - API integration ready')),
    );
  }
}

/// Empty schedules view - API focused
class _EmptySchedulesView extends StatelessWidget {
  const _EmptySchedulesView({
    this.hasError = false,
    this.errorMessage,
    this.onRetry,
  });

  final bool hasError;
  final String? errorMessage;
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context) {
    if (hasError) {
      return Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.cloud_off_outlined,
              size: 64,
              color: Colors.red[400],
            ),
            
            const SizedBox(height: 16),
            
            const Text(
              'API Connection Failed',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 8),
            
            Text(
              errorMessage ?? 'Unable to connect to http://10.0.2.2:3000/api/public/class-schedules',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black54,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 16),
            
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry API Call'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.event_busy_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          
          const SizedBox(height: 16),
          
          const Text(
            'No Classes Found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 8),
          
          const Text(
            'The API returned an empty response. No classes are scheduled for this date.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.black54,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 16),
          
          OutlinedButton.icon(
            onPressed: onRetry,
            icon: const Icon(Icons.refresh),
            label: const Text('Refresh from API'),
          ),
        ],
      ),
    );
  }
}

/// Add to calendar button
class _AddToCalendarButton extends StatelessWidget {
  const _AddToCalendarButton({
    required this.date,
    required this.onTap,
  });

  final DateTime date;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: FilledButton(
        onPressed: onTap,
        style: FilledButton.styleFrom(
          backgroundColor: Colors.black87,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          'Add to ${_getMonthName(date.month)} ${date.day}',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  String _getMonthName(int month) {
    const months = [
      '', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month];
  }
}
