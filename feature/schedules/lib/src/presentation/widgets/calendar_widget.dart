import 'package:flutter/material.dart';

/// Calendar widget for date selection
class CalendarWidget extends StatefulWidget {
  const CalendarWidget({
    super.key,
    required this.selectedDate,
    required this.onDateSelected,
  });

  final DateTime selectedDate;
  final ValueChanged<DateTime> onDateSelected;

  @override
  State<CalendarWidget> createState() => _CalendarWidgetState();
}

class _CalendarWidgetState extends State<CalendarWidget> {
  late DateTime _currentMonth;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _currentMonth = DateTime(widget.selectedDate.year, widget.selectedDate.month);
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Month header
          _MonthHeader(
            currentMonth: _currentMonth,
            onPreviousMonth: _goToPreviousMonth,
            onNextMonth: _goToNextMonth,
          ),
          
          const SizedBox(height: 20),
          
          // Week days header
          _WeekDaysHeader(),
          
          const SizedBox(height: 12),
          
          // Calendar grid
          _CalendarGrid(
            currentMonth: _currentMonth,
            selectedDate: widget.selectedDate,
            onDateSelected: widget.onDateSelected,
          ),
        ],
      ),
    );
  }

  void _goToPreviousMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month - 1);
    });
  }

  void _goToNextMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + 1);
    });
  }
}

/// Month header with navigation
class _MonthHeader extends StatelessWidget {
  const _MonthHeader({
    required this.currentMonth,
    required this.onPreviousMonth,
    required this.onNextMonth,
  });

  final DateTime currentMonth;
  final VoidCallback onPreviousMonth;
  final VoidCallback onNextMonth;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
          onPressed: onPreviousMonth,
          icon: const Icon(
            Icons.chevron_left,
            color: Colors.black54,
          ),
        ),
        
        Text(
          _getMonthYearText(currentMonth),
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        
        IconButton(
          onPressed: onNextMonth,
          icon: const Icon(
            Icons.chevron_right,
            color: Colors.black54,
          ),
        ),
      ],
    );
  }

  String _getMonthYearText(DateTime date) {
    const months = [
      '', 'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return '${months[date.month]} ${date.year}';
  }
}

/// Week days header
class _WeekDaysHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    const weekDays = ['Mon', 'Tue', 'Wed', 'Today', 'Fri', 'Sat', 'Sun'];
    
    return Row(
      children: weekDays.map((day) {
        final isToday = day == 'Today';
        return Expanded(
          child: Center(
            child: Text(
              day,
              style: TextStyle(
                fontSize: 14,
                fontWeight: isToday ? FontWeight.w600 : FontWeight.w400,
                color: isToday ? Colors.black87 : Colors.black54,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}

/// Calendar grid with dates
class _CalendarGrid extends StatelessWidget {
  const _CalendarGrid({
    required this.currentMonth,
    required this.selectedDate,
    required this.onDateSelected,
  });

  final DateTime currentMonth;
  final DateTime selectedDate;
  final ValueChanged<DateTime> onDateSelected;

  @override
  Widget build(BuildContext context) {
    final daysInMonth = _getDaysInMonth(currentMonth);
    final firstDayOfMonth = DateTime(currentMonth.year, currentMonth.month, 1);
    final startingWeekday = firstDayOfMonth.weekday;
    
    // Calculate the dates to show (including previous month's trailing dates)
    final dates = <DateTime>[];
    
    // Add trailing dates from previous month
    final previousMonth = DateTime(currentMonth.year, currentMonth.month - 1);
    final daysInPreviousMonth = _getDaysInMonth(previousMonth);
    for (int i = startingWeekday - 1; i > 0; i--) {
      dates.add(DateTime(previousMonth.year, previousMonth.month, daysInPreviousMonth - i + 1));
    }
    
    // Add current month dates
    for (int day = 1; day <= daysInMonth; day++) {
      dates.add(DateTime(currentMonth.year, currentMonth.month, day));
    }
    
    // Add leading dates from next month to fill the grid
    final nextMonth = DateTime(currentMonth.year, currentMonth.month + 1);
    int nextMonthDay = 1;
    while (dates.length < 42) { // 6 weeks * 7 days
      dates.add(DateTime(nextMonth.year, nextMonth.month, nextMonthDay));
      nextMonthDay++;
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1,
      ),
      itemCount: dates.length,
      itemBuilder: (context, index) {
        final date = dates[index];
        final isCurrentMonth = date.month == currentMonth.month;
        final isSelected = _isSameDay(date, selectedDate);
        final isToday = _isToday(date);
        
        return _CalendarDay(
          date: date,
          isCurrentMonth: isCurrentMonth,
          isSelected: isSelected,
          isToday: isToday,
          onTap: () => onDateSelected(date),
        );
      },
    );
  }

  int _getDaysInMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0).day;
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return _isSameDay(date, now);
  }
}

/// Individual calendar day
class _CalendarDay extends StatelessWidget {
  const _CalendarDay({
    required this.date,
    required this.isCurrentMonth,
    required this.isSelected,
    required this.isToday,
    required this.onTap,
  });

  final DateTime date;
  final bool isCurrentMonth;
  final bool isSelected;
  final bool isToday;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          margin: const EdgeInsets.all(2),
          decoration: BoxDecoration(
            color: isSelected ? Colors.black87 : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border: isToday && !isSelected
                ? Border.all(color: Colors.black87, width: 1)
                : null,
          ),
          child: Center(
            child: Text(
              '${date.day}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: isSelected || isToday ? FontWeight.w600 : FontWeight.w400,
                color: _getTextColor(),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Color _getTextColor() {
    if (isSelected) {
      return Colors.white;
    } else if (!isCurrentMonth) {
      return Colors.black26;
    } else if (isToday) {
      return Colors.black87;
    } else {
      return Colors.black54;
    }
  }
}
