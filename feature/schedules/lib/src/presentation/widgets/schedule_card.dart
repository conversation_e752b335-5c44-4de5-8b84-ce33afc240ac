import 'package:flutter/material.dart';
import '../../domain/models/class_schedule.dart';

/// Schedule card widget displaying class information
class ScheduleCard extends StatelessWidget {
  const ScheduleCard({
    super.key,
    required this.schedule,
    this.onTap,
    this.onBookTap,
    this.isBooking = false,
  });

  final ClassSchedule schedule;
  final VoidCallback? onTap;
  final VoidCallback? onBookTap;
  final bool isBooking;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          height: 120,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.08),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Class image
              _ClassImage(schedule: schedule),
              
              // Class content
              Expanded(
                child: _ClassContent(schedule: schedule),
              ),
              
              // Action button
              if (onBookTap != null)
                _ActionButton(
                  schedule: schedule,
                  onTap: onBookTap!,
                  isLoading: isBooking,
                ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Class image with placeholder
class _ClassImage extends StatelessWidget {
  const _ClassImage({required this.schedule});

  final ClassSchedule schedule;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 80,
      height: double.infinity,
      margin: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: _getClassColor(schedule.displayType),
      ),
      child: Stack(
        children: [
          // Background gradient
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  _getClassColor(schedule.displayType),
                  _getClassColor(schedule.displayType).withOpacity(0.7),
                ],
              ),
            ),
          ),
          
          // Class image or icon
          if (schedule.imageUrl != null)
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.asset(
                schedule.imageUrl!,
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _ClassIcon(schedule: schedule),
              ),
            )
          else
            _ClassIcon(schedule: schedule),
          
          // Time badge
          Positioned(
            top: 8,
            left: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                schedule.formattedStartTime,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getClassColor(String classType) {
    switch (classType.toLowerCase()) {
      case 'running':
      case 'cardio':
        return const Color(0xFFFF6B6B);
      case 'strength':
        return const Color(0xFF4ECDC4);
      case 'yoga':
        return const Color(0xFF45B7D1);
      case 'pilates':
        return const Color(0xFF96CEB4);
      case 'hiit':
        return const Color(0xFFFF9F43);
      default:
        return const Color(0xFF6C5CE7);
    }
  }
}

/// Class icon for placeholder
class _ClassIcon extends StatelessWidget {
  const _ClassIcon({required this.schedule});

  final ClassSchedule schedule;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Icon(
        _getClassIcon(schedule.displayType),
        size: 32,
        color: Colors.white.withOpacity(0.9),
      ),
    );
  }

  IconData _getClassIcon(String classType) {
    switch (classType.toLowerCase()) {
      case 'running':
      case 'cardio':
        return Icons.directions_run;
      case 'strength':
        return Icons.fitness_center;
      case 'yoga':
        return Icons.spa;
      case 'pilates':
        return Icons.self_improvement;
      case 'hiit':
        return Icons.local_fire_department;
      default:
        return Icons.sports_gymnastics;
    }
  }
}

/// Class content (title, instructor, details)
class _ClassContent extends StatelessWidget {
  const _ClassContent({required this.schedule});

  final ClassSchedule schedule;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Class title
          Text(
            schedule.displayName,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: 4),
          
          // Class type
          Text(
            schedule.displayType,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black54,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Instructor and location
          Row(
            children: [
              Expanded(
                child: Text(
                  schedule.displayInstructor,
                  style: const TextStyle(
                    fontSize: 13,
                    color: Colors.black54,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              
              const SizedBox(width: 8),
              
              Text(
                '•',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.black.withOpacity(0.3),
                ),
              ),
              
              const SizedBox(width: 8),
              
              Text(
                schedule.displayLocation,
                style: const TextStyle(
                  fontSize: 13,
                  color: Colors.black54,
                ),
              ),
            ],
          ),
          
          const Spacer(),
          
          // Duration and availability
          Row(
            children: [
              Text(
                schedule.durationText,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.black54,
                ),
              ),
              
              const SizedBox(width: 12),
              
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: schedule.isAvailable 
                      ? Colors.green.withOpacity(0.1)
                      : Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  schedule.availabilityText,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: schedule.isAvailable ? Colors.green[700] : Colors.red[700],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Action button (book/join)
class _ActionButton extends StatelessWidget {
  const _ActionButton({
    required this.schedule,
    required this.onTap,
    required this.isLoading,
  });

  final ClassSchedule schedule;
  final VoidCallback onTap;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (isLoading)
            const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.black54),
              ),
            )
          else
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: schedule.isAvailable ? onTap : null,
                borderRadius: BorderRadius.circular(20),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: schedule.isAvailable 
                        ? Colors.black87 
                        : Colors.grey[300],
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.arrow_forward,
                    color: schedule.isAvailable 
                        ? Colors.white 
                        : Colors.grey[500],
                    size: 20,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
