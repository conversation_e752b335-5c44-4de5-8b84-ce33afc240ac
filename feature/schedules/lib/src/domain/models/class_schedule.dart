import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'class_schedule.g.dart';

/// Class schedule model representing a scheduled fitness class
/// Updated to use actual API response fields for name and description
@JsonSerializable()
class ClassSchedule extends Equatable {
  const ClassSchedule({
    required this.id,
    required this.classId,
    required this.tenantId,
    required this.locationId,
    required this.facilityId,
    required this.startTime,
    required this.endTime,
    required this.duration,
    required this.createdAt,
    required this.updatedAt,
    this.staffId,
    this.startDate,
    this.endDate,
    this.calenderColor,
    this.repeatRule,
    this.pax,
    this.waitlist,
    this.allowClasspass,
    this.isPrivate,
    this.publishNow,
    this.publishAt,
    this.autoCancelIfMinimumNotMet,
    this.bookingWindowStart,
    this.bookingWindowEnd,
    this.checkInWindowStart,
    this.checkInWindowEnd,
    this.lateCancellationRule,
    this.name,
    this.description,
    this.className,
    this.instructorName,
    this.facilityName,
    this.locationName,
    this.classType,
    this.imageUrl,
  });

  /// Schedule unique identifier (string from API)
  final String id;
  
  /// Class identifier
  @JsonKey(name: 'class_id')
  final String classId;
  
  /// Tenant identifier
  @JsonKey(name: 'tenant_id')
  final int tenantId;
  
  /// Location identifier
  @JsonKey(name: 'location_id')
  final String locationId;
  
  /// Facility identifier
  @JsonKey(name: 'facility_id')
  final String facilityId;
  
  /// Staff/instructor identifier (nullable)
  @JsonKey(name: 'staff_id')
  final String? staffId;
  
  /// Schedule start time
  @JsonKey(name: 'start_time')
  final DateTime startTime;
  
  /// Schedule end time
  @JsonKey(name: 'end_time')
  final DateTime endTime;
  
  /// Class duration in minutes
  final int duration;
  
  /// Start date for recurring schedules (nullable)
  @JsonKey(name: 'start_date')
  final String? startDate;
  
  /// End date for recurring schedules (nullable)
  @JsonKey(name: 'end_date')
  final String? endDate;
  
  /// Calendar color
  @JsonKey(name: 'calender_color')
  final String? calenderColor;
  
  /// Repeat rule
  @JsonKey(name: 'repeat_rule')
  final String? repeatRule;
  
  /// Maximum capacity (pax from API)
  final int? pax;
  
  /// Waitlist capacity
  final int? waitlist;
  
  /// Allow classpass
  @JsonKey(name: 'allow_classpass')
  final bool? allowClasspass;
  
  /// Is private class
  @JsonKey(name: 'is_private')
  final bool? isPrivate;
  
  /// Publish now
  @JsonKey(name: 'publish_now')
  final bool? publishNow;
  
  /// Publish at
  @JsonKey(name: 'publish_at')
  final DateTime? publishAt;
  
  /// Auto cancel if minimum not met
  @JsonKey(name: 'auto_cancel_if_minimum_not_met')
  final bool? autoCancelIfMinimumNotMet;
  
  /// Booking window start
  @JsonKey(name: 'booking_window_start')
  final DateTime? bookingWindowStart;
  
  /// Booking window end
  @JsonKey(name: 'booking_window_end')
  final DateTime? bookingWindowEnd;
  
  /// Check in window start
  @JsonKey(name: 'check_in_window_start')
  final DateTime? checkInWindowStart;
  
  /// Check in window end
  @JsonKey(name: 'check_in_window_end')
  final DateTime? checkInWindowEnd;
  
  /// Late cancellation rule
  @JsonKey(name: 'late_cancellation_rule')
  final String? lateCancellationRule;
  
  /// Created timestamp
  @JsonKey(name: 'createdAt')
  final DateTime createdAt;
  
  /// Updated timestamp
  @JsonKey(name: 'updatedAt')
  final DateTime updatedAt;

  // Fields from API response for class information
  
  /// Class name from API (actual field from response)
  /// This is the primary source for class name
  final String? name;
  
  /// Class description from API (actual field from response)
  /// This is the primary source for class description
  final String? description;
  
  // Additional fields for UI display (legacy/fallback fields)
  
  /// Class name for display (legacy field, may be null)
  @JsonKey(name: 'class_name')
  final String? className;
  
  /// Instructor name for display
  @JsonKey(name: 'instructor_name')
  final String? instructorName;
  
  /// Facility name for display
  @JsonKey(name: 'facility_name')
  final String? facilityName;
  
  /// Location name for display
  @JsonKey(name: 'location_name')
  final String? locationName;
  
  /// Class type for display
  @JsonKey(name: 'class_type')
  final String? classType;
  
  /// Class image URL
  @JsonKey(name: 'image_url')
  final String? imageUrl;

  // Computed properties for UI
  
  /// Get display name for the class (prioritize API name field)
  String get displayName {
    // Priority: name (from API) > className (legacy) > class_id > fallback
    if (name != null && name!.trim().isNotEmpty) {
      return name!.trim();
    }
    if (className != null && className!.trim().isNotEmpty) {
      return className!.trim();
    }
    // Use class_id as fallback if available
    if (classId.isNotEmpty) {
      return 'Class ${classId.substring(0, 8)}'; // Show first 8 chars of ID
    }
    return 'Fitness Class'; // Final fallback
  }
  
  /// Get display description for the class (use API description field)
  String get displayDescription {
    if (description != null && description!.trim().isNotEmpty) {
      return description!.trim();
    }
    return 'Join this fitness class for a great workout experience.'; // Fallback
  }
  
  /// Get display instructor name
  String get displayInstructor => instructorName ?? 'Instructor';
  
  /// Get display location
  String get displayLocation => facilityName ?? locationName ?? 'Studio';
  
  /// Get display type
  String get displayType => classType ?? 'Fitness';
  
  /// Get formatted start time (HH:mm)
  String get formattedStartTime {
    return '${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}';
  }
  
  /// Get formatted end time (HH:mm)
  String get formattedEndTime {
    return '${endTime.hour.toString().padLeft(2, '0')}:${endTime.minute.toString().padLeft(2, '0')}';
  }
  
  /// Get formatted time range
  String get formattedTimeRange => '$formattedStartTime - $formattedEndTime';
  
  /// Get duration display text
  String get durationText => '${duration}min';
  
  /// Get maximum capacity (use pax field from API)
  int get maxCapacity => pax ?? 20;
  
  /// Get current bookings (use waitlist as placeholder)
  int get currentBookings => waitlist ?? 0;
  
  /// Check if class is active (use publishNow field)
  bool get isActive => publishNow ?? true;
  
  /// Check if class is available for booking
  bool get isAvailable => isActive && currentBookings < maxCapacity;
  
  /// Get availability status text
  String get availabilityText {
    if (!isActive) return 'Inactive';
    if (currentBookings >= maxCapacity) return 'Full';
    final remaining = maxCapacity - currentBookings;
    return '$remaining spots left';
  }
  
  /// Check if class is today
  bool get isToday {
    final now = DateTime.now();
    return startTime.year == now.year &&
           startTime.month == now.month &&
           startTime.day == now.day;
  }
  
  /// Check if class is upcoming (in the future)
  bool get isUpcoming => startTime.isAfter(DateTime.now());

  /// Creates a copy of this schedule with the given fields replaced
  ClassSchedule copyWith({
    String? id,
    String? classId,
    int? tenantId,
    String? locationId,
    String? facilityId,
    String? staffId,
    DateTime? startTime,
    DateTime? endTime,
    int? duration,
    String? startDate,
    String? endDate,
    String? calenderColor,
    String? repeatRule,
    int? pax,
    int? waitlist,
    bool? allowClasspass,
    bool? isPrivate,
    bool? publishNow,
    DateTime? publishAt,
    bool? autoCancelIfMinimumNotMet,
    DateTime? bookingWindowStart,
    DateTime? bookingWindowEnd,
    DateTime? checkInWindowStart,
    DateTime? checkInWindowEnd,
    String? lateCancellationRule,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? name,
    String? description,
    String? className,
    String? instructorName,
    String? facilityName,
    String? locationName,
    String? classType,
    String? imageUrl,
  }) {
    return ClassSchedule(
      id: id ?? this.id,
      classId: classId ?? this.classId,
      tenantId: tenantId ?? this.tenantId,
      locationId: locationId ?? this.locationId,
      facilityId: facilityId ?? this.facilityId,
      staffId: staffId ?? this.staffId,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      duration: duration ?? this.duration,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      calenderColor: calenderColor ?? this.calenderColor,
      repeatRule: repeatRule ?? this.repeatRule,
      pax: pax ?? this.pax,
      waitlist: waitlist ?? this.waitlist,
      allowClasspass: allowClasspass ?? this.allowClasspass,
      isPrivate: isPrivate ?? this.isPrivate,
      publishNow: publishNow ?? this.publishNow,
      publishAt: publishAt ?? this.publishAt,
      autoCancelIfMinimumNotMet: autoCancelIfMinimumNotMet ?? this.autoCancelIfMinimumNotMet,
      bookingWindowStart: bookingWindowStart ?? this.bookingWindowStart,
      bookingWindowEnd: bookingWindowEnd ?? this.bookingWindowEnd,
      checkInWindowStart: checkInWindowStart ?? this.checkInWindowStart,
      checkInWindowEnd: checkInWindowEnd ?? this.checkInWindowEnd,
      lateCancellationRule: lateCancellationRule ?? this.lateCancellationRule,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      name: name ?? this.name,
      description: description ?? this.description,
      className: className ?? this.className,
      instructorName: instructorName ?? this.instructorName,
      facilityName: facilityName ?? this.facilityName,
      locationName: locationName ?? this.locationName,
      classType: classType ?? this.classType,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }

  /// Creates a ClassSchedule from JSON
  factory ClassSchedule.fromJson(Map<String, dynamic> json) => _$ClassScheduleFromJson(json);

  /// Converts ClassSchedule to JSON
  Map<String, dynamic> toJson() => _$ClassScheduleToJson(this);

  @override
  List<Object?> get props => [
        id,
        classId,
        tenantId,
        locationId,
        facilityId,
        staffId,
        startTime,
        endTime,
        duration,
        startDate,
        endDate,
        calenderColor,
        repeatRule,
        pax,
        waitlist,
        allowClasspass,
        isPrivate,
        publishNow,
        publishAt,
        autoCancelIfMinimumNotMet,
        bookingWindowStart,
        bookingWindowEnd,
        checkInWindowStart,
        checkInWindowEnd,
        lateCancellationRule,
        createdAt,
        updatedAt,
        name,
        description,
        className,
        instructorName,
        facilityName,
        locationName,
        classType,
        imageUrl,
      ];

  @override
  String toString() => 'ClassSchedule(id: $id, name: ${displayName}, startTime: $startTime, instructor: $displayInstructor)';
}
