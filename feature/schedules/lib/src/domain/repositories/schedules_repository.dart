import '../models/class_schedule.dart';

/// Abstract repository interface for schedules data
/// 
/// This interface defines the contract for schedules data operations.
/// It can be implemented with different data sources (API, mock, local storage).
abstract interface class SchedulesRepository {
  /// Get all class schedules for a specific tenant
  Future<List<ClassSchedule>> getClassSchedules({
    required int tenantId,
    DateTime? startDate,
    DateTime? endDate,
  });

  /// Get schedules for today
  Future<List<ClassSchedule>> getTodaySchedules({
    required int tenantId,
  });

  /// Get schedules for a specific date
  Future<List<ClassSchedule>> getSchedulesForDate({
    required int tenantId,
    required DateTime date,
  });

  /// Get schedules for a date range
  Future<List<ClassSchedule>> getSchedulesForDateRange({
    required int tenantId,
    required DateTime startDate,
    required DateTime endDate,
  });

  /// Get upcoming schedules
  Future<List<ClassSchedule>> getUpcomingSchedules({
    required int tenantId,
    int? limit,
  });

  /// Get schedule by ID
  Future<ClassSchedule?> getScheduleById({
    required String scheduleId,
    required int tenantId,
  });

  /// Search schedules by class name or instructor
  Future<List<ClassSchedule>> searchSchedules({
    required int tenantId,
    required String query,
  });

  /// Book a class schedule (if booking functionality is needed)
  Future<void> bookSchedule({
    required String scheduleId,
    required int tenantId,
  });

  /// Cancel a booking (if booking functionality is needed)
  Future<void> cancelBooking({
    required String scheduleId,
    required int tenantId,
  });
}
