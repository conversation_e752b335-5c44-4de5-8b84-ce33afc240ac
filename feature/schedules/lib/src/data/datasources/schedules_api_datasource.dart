import 'package:rest_client/rest_client.dart';
import '../../domain/models/class_schedule.dart';

/// API data source for schedules data - API ONLY with proper response parsing and type safety
/// 
/// This class handles all HTTP requests related to class schedules.
/// It uses the rest_client from the core module for network operations.
class SchedulesApiDataSource {
  const SchedulesApiDataSource({
    required RestClient restClient,
  }) : _restClient = restClient;

  final RestClient _restClient;

  /// Format DateTime to API-compatible date string (YYYY-MM-DD)
  String _formatDateForApi(DateTime date) {
    return '${date.year.toString().padLeft(4, '0')}-'
           '${date.month.toString().padLeft(2, '0')}-'
           '${date.day.toString().padLeft(2, '0')}';
  }

  /// Get all class schedules for a specific tenant
  Future<List<ClassSchedule>> getClassSchedules({
    required int tenantId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, String?>{
        'tenantId': tenantId.toString(),
      };

      if (startDate != null) {
        queryParams['startDate'] = _formatDateForApi(startDate);
      }

      if (endDate != null) {
        queryParams['endDate'] = _formatDateForApi(endDate);
      }

      // Debug logging for API calls
      print('🌐 API Call: GET /api/public/class-schedules');
      print('📋 Query Params: $queryParams');

      final response = await _restClient.get(
        '/api/public/class-schedules',
        queryParams: queryParams,
      );

      print('📥 API Response received: ${response != null ? 'Success' : 'Null'}');

      if (response != null) {
        print('📥 Raw API Response type: ${response.runtimeType}');
        print('📥 Raw API Response preview: ${response.toString().substring(0, response.toString().length > 300 ? 300 : response.toString().length)}...');
        
        // Handle direct array response
        if (response is List) {
          final schedules = (response as List)
              .map((json) => ClassSchedule.fromJson(json as Map<String, dynamic>))
              .toList();
          print('✅ Parsed ${schedules.length} schedules from direct array');
          return schedules;
        }
        
        // Handle API response with success/data structure
        if (response is Map<String, dynamic>) {
          // Check for success + data.schedules structure (actual API format)
          if (response['success'] == true && 
              response['data'] is Map) {
            final dataMap = response['data'] as Map<String, dynamic>;
            if (dataMap['schedules'] is List) {
              final schedules = (dataMap['schedules'] as List)
                  .map((json) => ClassSchedule.fromJson(json as Map<String, dynamic>))
                  .toList();
              print('✅ Parsed ${schedules.length} schedules from API success response');
              print('📊 Total from API: ${dataMap['total']}');
              print('📄 Has more: ${dataMap['hasMore']}');
              return schedules;
            }
          }
          
          // Handle simple wrapped response with 'data' field
          if (response['data'] is List) {
            final schedules = (response['data'] as List)
                .map((json) => ClassSchedule.fromJson(json as Map<String, dynamic>))
                .toList();
            print('✅ Parsed ${schedules.length} schedules from wrapped response');
            return schedules;
          }
          
          // Handle schedules field directly
          if (response['schedules'] is List) {
            final schedules = (response['schedules'] as List)
                .map((json) => ClassSchedule.fromJson(json as Map<String, dynamic>))
                .toList();
            print('✅ Parsed ${schedules.length} schedules from schedules field');
            return schedules;
          }
          
          // Debug: Print available keys in response
          print('🔍 Available keys in response: ${response.keys.toList()}');
          if (response['data'] is Map) {
            print('🔍 Available keys in data: ${(response['data'] as Map).keys.toList()}');
          }
        }
      }

      print('⚠️ No valid data found in API response');
      return [];
    } catch (e) {
      print('❌ API Error: $e');
      throw SchedulesApiException('Failed to fetch class schedules: $e');
    }
  }

  /// Get schedules for today
  Future<List<ClassSchedule>> getTodaySchedules({
    required int tenantId,
  }) async {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);

    print('📅 Getting today\'s schedules for: ${_formatDateForApi(startOfDay)}');

    return getClassSchedules(
      tenantId: tenantId,
      startDate: startOfDay,
      endDate: endOfDay,
    );
  }

  /// Get schedules for a specific date
  Future<List<ClassSchedule>> getSchedulesForDate({
    required int tenantId,
    required DateTime date,
  }) async {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

    print('📅 Getting schedules for date: ${_formatDateForApi(startOfDay)}');

    return getClassSchedules(
      tenantId: tenantId,
      startDate: startOfDay,
      endDate: endOfDay,
    );
  }

  /// Get schedules for a date range
  Future<List<ClassSchedule>> getSchedulesForDateRange({
    required int tenantId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    print('📅 Getting schedules for range: ${_formatDateForApi(startDate)} to ${_formatDateForApi(endDate)}');

    return getClassSchedules(
      tenantId: tenantId,
      startDate: startDate,
      endDate: endDate,
    );
  }

  /// Get upcoming schedules
  Future<List<ClassSchedule>> getUpcomingSchedules({
    required int tenantId,
    int? limit,
  }) async {
    try {
      final now = DateTime.now();
      final queryParams = <String, String?>{
        'tenantId': tenantId.toString(),
        'startDate': _formatDateForApi(now),
      };

      if (limit != null) {
        queryParams['limit'] = limit.toString();
      }

      print('🔮 Getting upcoming schedules from: ${_formatDateForApi(now)}');
      print('📋 Query Params: $queryParams');

      final response = await _restClient.get(
        '/api/public/class-schedules',
        queryParams: queryParams,
      );

      if (response != null) {
        List<ClassSchedule> schedules = [];
        
        // Handle API response with success/data structure
        if (response is Map<String, dynamic> && 
            response['success'] == true && 
            response['data'] is Map) {
          final dataMap = response['data'] as Map<String, dynamic>;
          if (dataMap['schedules'] is List) {
            schedules = (dataMap['schedules'] as List)
                .map((json) => ClassSchedule.fromJson(json as Map<String, dynamic>))
                .toList();
          }
        } else if (response is List) {
          schedules = (response as List)
              .map((json) => ClassSchedule.fromJson(json as Map<String, dynamic>))
              .toList();
        } else if (response is Map<String, dynamic> && response['data'] is List) {
          schedules = (response['data'] as List)
              .map((json) => ClassSchedule.fromJson(json as Map<String, dynamic>))
              .toList();
        }

        // Sort by start time and limit if specified
        schedules.sort((a, b) => a.startTime.compareTo(b.startTime));
        
        if (limit != null && schedules.length > limit) {
          final limitedSchedules = schedules.take(limit).toList();
          print('✅ Returning ${limitedSchedules.length} upcoming schedules (limited from ${schedules.length})');
          return limitedSchedules;
        }

        print('✅ Returning ${schedules.length} upcoming schedules');
        return schedules;
      }

      print('⚠️ No upcoming schedules found');
      return [];
    } catch (e) {
      print('❌ Error fetching upcoming schedules: $e');
      throw SchedulesApiException('Failed to fetch upcoming schedules: $e');
    }
  }

  /// Get schedule by ID
  Future<ClassSchedule?> getScheduleById({
    required String scheduleId, // Changed to String to match API
    required int tenantId,
  }) async {
    try {
      print('🔍 Getting schedule by ID: $scheduleId');

      final response = await _restClient.get(
        '/api/public/class-schedules/$scheduleId',
        queryParams: {
          'tenantId': tenantId.toString(),
        },
      );

      if (response != null) {
        if (response is Map<String, dynamic> && 
            response['success'] == true && 
            response['data'] is Map) {
          final schedule = ClassSchedule.fromJson(response['data'] as Map<String, dynamic>);
          print('✅ Found schedule: ${schedule.displayName}');
          return schedule;
        } else if (response is Map<String, dynamic> && response['data'] != null) {
          final schedule = ClassSchedule.fromJson(response['data'] as Map<String, dynamic>);
          print('✅ Found schedule: ${schedule.displayName}');
          return schedule;
        } else if (response is Map<String, dynamic>) {
          final schedule = ClassSchedule.fromJson(response);
          print('✅ Found schedule: ${schedule.displayName}');
          return schedule;
        }
      }

      print('⚠️ Schedule not found');
      return null;
    } catch (e) {
      print('❌ Error fetching schedule by ID: $e');
      throw SchedulesApiException('Failed to fetch schedule by ID: $e');
    }
  }

  /// Search schedules by class name or instructor
  Future<List<ClassSchedule>> searchSchedules({
    required int tenantId,
    required String query,
  }) async {
    try {
      print('🔍 Searching schedules with query: "$query"');

      final response = await _restClient.get(
        '/api/public/class-schedules/search',
        queryParams: {
          'tenantId': tenantId.toString(),
          'q': query,
        },
      );

      if (response != null) {
        List<ClassSchedule> schedules = [];
        
        // Handle API response with success/data structure
        if (response is Map<String, dynamic> && 
            response['success'] == true && 
            response['data'] is Map) {
          final dataMap = response['data'] as Map<String, dynamic>;
          if (dataMap['schedules'] is List) {
            schedules = (dataMap['schedules'] as List)
                .map((json) => ClassSchedule.fromJson(json as Map<String, dynamic>))
                .toList();
          }
        } else if (response is List) {
          schedules = (response as List)
              .map((json) => ClassSchedule.fromJson(json as Map<String, dynamic>))
              .toList();
        } else if (response is Map<String, dynamic> && response['data'] is List) {
          schedules = (response['data'] as List)
              .map((json) => ClassSchedule.fromJson(json as Map<String, dynamic>))
              .toList();
        }

        print('✅ Found ${schedules.length} schedules matching "$query"');
        return schedules;
      }

      print('⚠️ No search results found');
      return [];
    } catch (e) {
      print('❌ Error searching schedules: $e');
      throw SchedulesApiException('Failed to search schedules: $e');
    }
  }

  /// Book a class schedule
  Future<void> bookSchedule({
    required String scheduleId, // Changed to String to match API
    required int tenantId,
  }) async {
    try {
      print('📝 Booking schedule ID: $scheduleId');

      await _restClient.post(
        '/api/public/class-schedules/$scheduleId/book',
        body: {
          'tenantId': tenantId,
        },
      );

      print('✅ Successfully booked schedule');
    } catch (e) {
      print('❌ Error booking schedule: $e');
      throw SchedulesApiException('Failed to book schedule: $e');
    }
  }

  /// Cancel a booking
  Future<void> cancelBooking({
    required String scheduleId, // Changed to String to match API
    required int tenantId,
  }) async {
    try {
      print('❌ Cancelling booking for schedule ID: $scheduleId');

      await _restClient.delete(
        '/api/public/class-schedules/$scheduleId/book',
        queryParams: {
          'tenantId': tenantId.toString(),
        },
      );

      print('✅ Successfully cancelled booking');
    } catch (e) {
      print('❌ Error cancelling booking: $e');
      throw SchedulesApiException('Failed to cancel booking: $e');
    }
  }
}

/// Custom exception for schedules API operations
class SchedulesApiException implements Exception {
  const SchedulesApiException(this.message);
  
  final String message;
  
  @override
  String toString() => 'SchedulesApiException: $message';
}
