import '../../domain/models/class_schedule.dart';
import '../../domain/repositories/schedules_repository.dart';
import '../datasources/schedules_api_datasource.dart';

/// Implementation of SchedulesRepository with REAL API ONLY
/// 
/// This implementation ONLY uses real API calls without any mock data fallback.
/// All data comes directly from the API endpoint.
class SchedulesRepositoryImpl implements SchedulesRepository {
  const SchedulesRepositoryImpl({
    required SchedulesApiDataSource apiDataSource,
  }) : _apiDataSource = apiDataSource;

  final SchedulesApiDataSource _apiDataSource;

  @override
  Future<List<ClassSchedule>> getClassSchedules({
    required int tenantId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    return await _apiDataSource.getClassSchedules(
      tenantId: tenantId,
      startDate: startDate,
      endDate: endDate,
    );
  }

  @override
  Future<List<ClassSchedule>> getTodaySchedules({
    required int tenantId,
  }) async {
    return await _apiDataSource.getTodaySchedules(tenantId: tenantId);
  }

  @override
  Future<List<ClassSchedule>> getSchedulesForDate({
    required int tenantId,
    required DateTime date,
  }) async {
    return await _apiDataSource.getSchedulesForDate(
      tenantId: tenantId,
      date: date,
    );
  }

  @override
  Future<List<ClassSchedule>> getSchedulesForDateRange({
    required int tenantId,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    return await _apiDataSource.getSchedulesForDateRange(
      tenantId: tenantId,
      startDate: startDate,
      endDate: endDate,
    );
  }

  @override
  Future<List<ClassSchedule>> getUpcomingSchedules({
    required int tenantId,
    int? limit,
  }) async {
    return await _apiDataSource.getUpcomingSchedules(
      tenantId: tenantId,
      limit: limit,
    );
  }

  @override
  Future<ClassSchedule?> getScheduleById({
    required String scheduleId,
    required int tenantId,
  }) async {
    return await _apiDataSource.getScheduleById(
      scheduleId: scheduleId,
      tenantId: tenantId,
    );
  }

  @override
  Future<List<ClassSchedule>> searchSchedules({
    required int tenantId,
    required String query,
  }) async {
    return await _apiDataSource.searchSchedules(
      tenantId: tenantId,
      query: query,
    );
  }

  @override
  Future<void> bookSchedule({
    required String scheduleId,
    required int tenantId,
  }) async {
    await _apiDataSource.bookSchedule(
      scheduleId: scheduleId,
      tenantId: tenantId,
    );
  }

  @override
  Future<void> cancelBooking({
    required String scheduleId,
    required int tenantId,
  }) async {
    await _apiDataSource.cancelBooking(
      scheduleId: scheduleId,
      tenantId: tenantId,
    );
  }
}

/// Custom exception for schedules repository operations
class SchedulesRepositoryException implements Exception {
  const SchedulesRepositoryException(this.message);
  
  final String message;
  
  @override
  String toString() => 'SchedulesRepositoryException: $message';
}
