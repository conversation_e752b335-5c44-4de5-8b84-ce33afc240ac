import 'package:flutter_test/flutter_test.dart';

/// Test to verify date formatting for API calls
void main() {
  group('API Date Formatting Tests', () {
    /// Format DateTime to API-compatible date string (YYYY-MM-DD)
    String formatDateForApi(DateTime date) {
      return '${date.year.toString().padLeft(4, '0')}-'
             '${date.month.toString().padLeft(2, '0')}-'
             '${date.day.toString().padLeft(2, '0')}';
    }

    test('should format dates correctly for API', () {
      // Test various dates
      expect(formatDateForApi(DateTime(2025, 7, 9)), equals('2025-07-09'));
      expect(formatDateForApi(DateTime(2025, 7, 16)), equals('2025-07-16'));
      expect(formatDateForApi(DateTime(2025, 12, 31)), equals('2025-12-31'));
      expect(formatDateForApi(DateTime(2025, 1, 1)), equals('2025-01-01'));
      
      // Test with time components (should be ignored)
      expect(formatDateForApi(DateTime(2025, 7, 9, 14, 30, 45)), equals('2025-07-09'));
    });

    test('should handle today\'s date correctly', () {
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      final endOfDay = DateTime(today.year, today.month, today.day, 23, 59, 59);
      
      final startFormatted = formatDateForApi(startOfDay);
      final endFormatted = formatDateForApi(endOfDay);
      
      // Both should be the same date string
      expect(startFormatted, equals(endFormatted));
      
      // Should match expected format
      final expectedPattern = RegExp(r'^\d{4}-\d{2}-\d{2}$');
      expect(startFormatted, matches(expectedPattern));
    });

    test('should handle date ranges correctly', () {
      final startDate = DateTime(2025, 7, 9);
      final endDate = DateTime(2025, 7, 16);
      
      final startFormatted = formatDateForApi(startDate);
      final endFormatted = formatDateForApi(endDate);
      
      expect(startFormatted, equals('2025-07-09'));
      expect(endFormatted, equals('2025-07-16'));
      
      // Verify the range makes sense
      expect(startDate.isBefore(endDate), isTrue);
    });

    test('should generate correct API URL format', () {
      final tenantId = 1;
      final startDate = DateTime(2025, 7, 9);
      final endDate = DateTime(2025, 7, 16);
      
      final queryParams = <String, String>{
        'tenantId': tenantId.toString(),
        'startDate': formatDateForApi(startDate),
        'endDate': formatDateForApi(endDate),
      };
      
      // Simulate URL construction
      final baseUrl = 'http://10.0.2.2:3000/api/public/class-schedules';
      final queryString = queryParams.entries
          .map((e) => '${e.key}=${e.value}')
          .join('&');
      final fullUrl = '$baseUrl?$queryString';
      
      expect(fullUrl, equals('http://10.0.2.2:3000/api/public/class-schedules?tenantId=1&startDate=2025-07-09&endDate=2025-07-16'));
    });
  });
}
