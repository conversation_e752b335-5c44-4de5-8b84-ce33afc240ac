import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'data/datasources/dashboard_mock_datasource.dart';
import 'data/repositories/dashboard_repository_impl.dart';
import 'domain/repositories/dashboard_repository.dart';
import 'presentation/bloc/dashboard_bloc.dart';

/// Dependency injection for dashboard feature
class DashboardInjection {
  /// Create dashboard repository
  static DashboardRepository createRepository() {
    final dataSource = DashboardMockDataSource();
    return DashboardRepositoryImpl(dataSource: dataSource);
  }

  /// Create dashboard bloc
  static DashboardBloc createBloc() {
    final repository = createRepository();
    return DashboardBloc(repository: repository);
  }
}

/// Dashboard scope widget that provides dashboard dependencies
class DashboardScope extends StatelessWidget {
  const DashboardScope({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => DashboardInjection.createBloc(),
      child: child,
    );
  }

  /// Get dashboard bloc from context
  static DashboardBloc blocOf(BuildContext context) {
    return context.read<DashboardBloc>();
  }

  /// Watch dashboard bloc from context
  static DashboardBloc watchBlocOf(BuildContext context) {
    return context.watch<DashboardBloc>();
  }
}
