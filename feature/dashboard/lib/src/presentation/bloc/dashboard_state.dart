import 'package:equatable/equatable.dart';
import '../../domain/models/user.dart';
import '../../domain/models/program.dart';
import '../../domain/models/workout.dart';

/// Dashboard state status enum
enum DashboardStatus {
  initial,
  loading,
  success,
  failure,
}

/// Dashboard state class
class DashboardState extends Equatable {
  const DashboardState({
    this.status = DashboardStatus.initial,
    this.user,
    this.currentProgram,
    this.todayWorkouts = const [],
    this.allWorkouts = const [],
    this.filteredWorkouts = const [],
    this.searchQuery = '',
    this.selectedCategoryId,
    this.errorMessage,
    this.isRefreshing = false,
  });

  /// Current status of the dashboard
  final DashboardStatus status;

  /// Current user profile
  final User? user;

  /// User's current program with progress
  final Program? currentProgram;

  /// Today's recommended workouts
  final List<Workout> todayWorkouts;

  /// All available workouts
  final List<Workout> allWorkouts;

  /// Filtered workouts based on search/category
  final List<Workout> filteredWorkouts;

  /// Current search query
  final String searchQuery;

  /// Selected category filter ID
  final String? selectedCategoryId;

  /// Error message if any
  final String? errorMessage;

  /// Whether data is being refreshed
  final bool isRefreshing;

  /// Whether the dashboard is in loading state
  bool get isLoading => status == DashboardStatus.loading;

  /// Whether the dashboard has data
  bool get hasData => status == DashboardStatus.success;

  /// Whether the dashboard has an error
  bool get hasError => status == DashboardStatus.failure;

  /// Whether search is active
  bool get isSearchActive => searchQuery.isNotEmpty;

  /// Whether category filter is active
  bool get isCategoryFilterActive => selectedCategoryId != null;

  /// Whether any filters are active
  bool get hasActiveFilters => isSearchActive || isCategoryFilterActive;

  /// Get workouts to display (filtered or today's workouts)
  List<Workout> get displayWorkouts {
    if (hasActiveFilters) {
      return filteredWorkouts;
    }
    return todayWorkouts;
  }

  /// Create a copy of this state with updated values
  DashboardState copyWith({
    DashboardStatus? status,
    User? user,
    Program? currentProgram,
    List<Workout>? todayWorkouts,
    List<Workout>? allWorkouts,
    List<Workout>? filteredWorkouts,
    String? searchQuery,
    String? selectedCategoryId,
    String? errorMessage,
    bool? isRefreshing,
  }) {
    return DashboardState(
      status: status ?? this.status,
      user: user ?? this.user,
      currentProgram: currentProgram ?? this.currentProgram,
      todayWorkouts: todayWorkouts ?? this.todayWorkouts,
      allWorkouts: allWorkouts ?? this.allWorkouts,
      filteredWorkouts: filteredWorkouts ?? this.filteredWorkouts,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedCategoryId: selectedCategoryId ?? this.selectedCategoryId,
      errorMessage: errorMessage ?? this.errorMessage,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }

  /// Create a copy with cleared filters
  DashboardState copyWithClearedFilters() {
    return copyWith(
      searchQuery: '',
      selectedCategoryId: null,
      filteredWorkouts: [],
    );
  }

  @override
  List<Object?> get props => [
        status,
        user,
        currentProgram,
        todayWorkouts,
        allWorkouts,
        filteredWorkouts,
        searchQuery,
        selectedCategoryId,
        errorMessage,
        isRefreshing,
      ];

  @override
  String toString() => 'DashboardState(status: $status, user: ${user?.name}, '
      'todayWorkouts: ${todayWorkouts.length}, searchQuery: $searchQuery)';
}
