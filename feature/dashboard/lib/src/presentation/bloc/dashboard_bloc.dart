import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/repositories/dashboard_repository.dart';
import '../../domain/models/user.dart';
import '../../domain/models/program.dart';
import '../../domain/models/workout.dart';
import 'dashboard_event.dart';
import 'dashboard_state.dart';

/// Bloc for managing dashboard state and business logic
class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  DashboardBloc({
    required DashboardRepository repository,
  })  : _repository = repository,
        super(const DashboardState()) {
    on<DashboardLoadRequested>(_onLoadRequested);
    on<DashboardRefreshRequested>(_onRefreshRequested);
    on<WorkoutCompletionToggled>(_onWorkoutCompletionToggled);
    on<WorkoutFavoriteToggled>(_onWorkoutFavoriteToggled);
    on<WorkoutSearchRequested>(_onWorkoutSearchRequested);
    on<WorkoutCategoryFilterRequested>(_onWorkoutCategoryFilterRequested);
    on<WorkoutFiltersCleared>(_onWorkoutFiltersCleared);
  }

  final DashboardRepository _repository;

  /// Handle dashboard load request
  Future<void> _onLoadRequested(
    DashboardLoadRequested event,
    Emitter<DashboardState> emit,
  ) async {
    emit(state.copyWith(status: DashboardStatus.loading));

    try {
      // Load all dashboard data concurrently
      final results = await Future.wait([
        _repository.getCurrentUser(),
        _repository.getCurrentProgram(),
        _repository.getTodayWorkouts(),
        _repository.getAllWorkouts(),
      ]);

      emit(state.copyWith(
        status: DashboardStatus.success,
        user: results[0] as User,
        currentProgram: results[1] as Program?,
        todayWorkouts: results[2] as List<Workout>,
        allWorkouts: results[3] as List<Workout>,
        errorMessage: null,
      ));
    } catch (error) {
      emit(state.copyWith(
        status: DashboardStatus.failure,
        errorMessage: error.toString(),
      ));
    }
  }

  /// Handle dashboard refresh request
  Future<void> _onRefreshRequested(
    DashboardRefreshRequested event,
    Emitter<DashboardState> emit,
  ) async {
    emit(state.copyWith(isRefreshing: true));

    try {
      final results = await Future.wait([
        _repository.getCurrentUser(),
        _repository.getCurrentProgram(),
        _repository.getTodayWorkouts(),
        _repository.getAllWorkouts(),
      ]);

      emit(state.copyWith(
        status: DashboardStatus.success,
        user: results[0] as User,
        currentProgram: results[1] as Program?,
        todayWorkouts: results[2] as List<Workout>,
        allWorkouts: results[3] as List<Workout>,
        isRefreshing: false,
        errorMessage: null,
      ));
    } catch (error) {
      emit(state.copyWith(
        isRefreshing: false,
        errorMessage: error.toString(),
      ));
    }
  }

  /// Handle workout completion toggle
  Future<void> _onWorkoutCompletionToggled(
    WorkoutCompletionToggled event,
    Emitter<DashboardState> emit,
  ) async {
    try {
      await _repository.markWorkoutCompleted(event.workoutId);
      
      // Update local state
      final updatedTodayWorkouts = state.todayWorkouts.map((workout) {
        if (workout.id == event.workoutId) {
          return workout.copyWith(isCompleted: !workout.isCompleted);
        }
        return workout;
      }).toList();

      final updatedAllWorkouts = state.allWorkouts.map((workout) {
        if (workout.id == event.workoutId) {
          return workout.copyWith(isCompleted: !workout.isCompleted);
        }
        return workout;
      }).toList();

      emit(state.copyWith(
        todayWorkouts: updatedTodayWorkouts,
        allWorkouts: updatedAllWorkouts,
      ));
    } catch (error) {
      emit(state.copyWith(errorMessage: error.toString()));
    }
  }

  /// Handle workout favorite toggle
  Future<void> _onWorkoutFavoriteToggled(
    WorkoutFavoriteToggled event,
    Emitter<DashboardState> emit,
  ) async {
    try {
      await _repository.toggleWorkoutFavorite(event.workoutId);
      
      // Update local state
      final updatedTodayWorkouts = state.todayWorkouts.map((workout) {
        if (workout.id == event.workoutId) {
          return workout.copyWith(isFavorite: !workout.isFavorite);
        }
        return workout;
      }).toList();

      final updatedAllWorkouts = state.allWorkouts.map((workout) {
        if (workout.id == event.workoutId) {
          return workout.copyWith(isFavorite: !workout.isFavorite);
        }
        return workout;
      }).toList();

      emit(state.copyWith(
        todayWorkouts: updatedTodayWorkouts,
        allWorkouts: updatedAllWorkouts,
      ));
    } catch (error) {
      emit(state.copyWith(errorMessage: error.toString()));
    }
  }

  /// Handle workout search
  Future<void> _onWorkoutSearchRequested(
    WorkoutSearchRequested event,
    Emitter<DashboardState> emit,
  ) async {
    try {
      final filteredWorkouts = await _repository.searchWorkouts(event.query);
      
      emit(state.copyWith(
        searchQuery: event.query,
        filteredWorkouts: filteredWorkouts,
        selectedCategoryId: null, // Clear category filter when searching
      ));
    } catch (error) {
      emit(state.copyWith(errorMessage: error.toString()));
    }
  }

  /// Handle category filter
  Future<void> _onWorkoutCategoryFilterRequested(
    WorkoutCategoryFilterRequested event,
    Emitter<DashboardState> emit,
  ) async {
    try {
      final filteredWorkouts = await _repository.getWorkoutsByCategory(event.categoryId);
      
      emit(state.copyWith(
        selectedCategoryId: event.categoryId,
        filteredWorkouts: filteredWorkouts,
        searchQuery: '', // Clear search when filtering by category
      ));
    } catch (error) {
      emit(state.copyWith(errorMessage: error.toString()));
    }
  }

  /// Handle clearing filters
  Future<void> _onWorkoutFiltersCleared(
    WorkoutFiltersCleared event,
    Emitter<DashboardState> emit,
  ) async {
    emit(state.copyWithClearedFilters());
  }
}
