import 'package:equatable/equatable.dart';

/// Base class for all dashboard events
sealed class DashboardEvent extends Equatable {
  const DashboardEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load initial dashboard data
class DashboardLoadRequested extends DashboardEvent {
  const DashboardLoadRequested();
}

/// Event to refresh dashboard data
class DashboardRefreshRequested extends DashboardEvent {
  const DashboardRefreshRequested();
}

/// Event to mark a workout as completed
class WorkoutCompletionToggled extends DashboardEvent {
  const WorkoutCompletionToggled({required this.workoutId});

  final String workoutId;

  @override
  List<Object?> get props => [workoutId];
}

/// Event to toggle workout favorite status
class WorkoutFavoriteToggled extends DashboardEvent {
  const WorkoutFavoriteToggled({required this.workoutId});

  final String workoutId;

  @override
  List<Object?> get props => [workoutId];
}

/// Event to search workouts
class WorkoutSearchRequested extends DashboardEvent {
  const WorkoutSearchRequested({required this.query});

  final String query;

  @override
  List<Object?> get props => [query];
}

/// Event to filter workouts by category
class WorkoutCategoryFilterRequested extends DashboardEvent {
  const WorkoutCategoryFilterRequested({required this.categoryId});

  final String categoryId;

  @override
  List<Object?> get props => [categoryId];
}

/// Event to clear workout filters
class WorkoutFiltersCleared extends DashboardEvent {
  const WorkoutFiltersCleared();
}
