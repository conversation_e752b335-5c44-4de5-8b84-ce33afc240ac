import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/dashboard_bloc.dart';
import '../bloc/dashboard_event.dart';
import '../bloc/dashboard_state.dart';
import 'dashboard_header.dart';
import 'program_progress_card.dart';
import 'workout_card.dart';
import 'search_screen.dart';
import 'workout_details_screen.dart';
import 'program_details_screen.dart';
import '../../domain/models/workout.dart';

/// Main dashboard screen widget
class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    // Load dashboard data when screen initializes
    context.read<DashboardBloc>().add(const DashboardLoadRequested());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: BlocConsumer<DashboardBloc, DashboardState>(
          listener: (context, state) {
            // Show error messages
            if (state.hasError && state.errorMessage != null) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.errorMessage!),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
            }
          },
          builder: (context, state) {
            if (state.isLoading) {
              return const _LoadingView();
            }

            if (state.hasError && !state.hasData) {
              return _ErrorView(
                message: state.errorMessage ?? 'Something went wrong',
                onRetry: () => context.read<DashboardBloc>().add(
                  const DashboardLoadRequested(),
                ),
              );
            }

            return RefreshIndicator(
              onRefresh: () async {
                context.read<DashboardBloc>().add(const DashboardRefreshRequested());
              },
              child: _DashboardContent(state: state),
            );
          },
        ),
      ),
    );
  }
}

/// Loading view widget
class _LoadingView extends StatelessWidget {
  const _LoadingView();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }
}

/// Error view widget
class _ErrorView extends StatelessWidget {
  const _ErrorView({
    required this.message,
    required this.onRetry,
  });

  final String message;
  final VoidCallback onRetry;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: theme.colorScheme.error,
            ),
            
            const SizedBox(height: 16),
            
            Text(
              'Oops! Something went wrong',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 8),
            
            Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 24),
            
            FilledButton(
              onPressed: onRetry,
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }
}

/// Main dashboard content
class _DashboardContent extends StatelessWidget {
  const _DashboardContent({required this.state});

  final DashboardState state;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return CustomScrollView(
      slivers: [
        // Header
        if (state.user != null)
          SliverToBoxAdapter(
            child: DashboardHeader(
              user: state.user!,
              onSearchTap: () => _showSearchDialog(context),
              onCalendarTap: () => _showCalendarDialog(context),
              onClassesTap: () => _showClassesDialog(context),
            ),
          ),

        // Program progress card
        if (state.currentProgram != null)
          SliverToBoxAdapter(
            child: Column(
              children: [
                const SizedBox(height: 8),
                ProgramProgressCard(
                  program: state.currentProgram!,
                  onTap: () => _showProgramDetails(context, state),
                ),
                const SizedBox(height: 32),
              ],
            ),
          ),

        // "What will you accomplish today?" section
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              'What will you\naccomplish today?',
              style: theme.textTheme.headlineMedium?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w700,
                height: 1.2,
              ),
            ),
          ),
        ),

        const SliverToBoxAdapter(child: SizedBox(height: 24)),

        // Workout cards
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final workout = state.displayWorkouts[index];
                return WorkoutCard(
                  workout: workout,
                  onTap: () => _showWorkoutDetails(context, workout),
                  onFavoriteTap: () => context.read<DashboardBloc>().add(
                    WorkoutFavoriteToggled(workoutId: workout.id),
                  ),
                );
              },
              childCount: state.displayWorkouts.length,
            ),
          ),
        ),

        // Bottom padding for navigation
        const SliverToBoxAdapter(child: SizedBox(height: 100)),
      ],
    );
  }

  void _showSearchDialog(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SearchScreen(),
      ),
    );
  }

  void _showCalendarDialog(BuildContext context) {
    // TODO: Implement calendar functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Calendar functionality coming soon!')),
    );
  }

  void _showClassesDialog(BuildContext context) {
    // TODO: Implement classes functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Classes functionality coming soon!')),
    );
  }

  void _showProgramDetails(BuildContext context, DashboardState state) {
    if (state.currentProgram != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ProgramDetailsScreen(program: state.currentProgram!),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Program details coming soon!')),
      );
    }
  }

  void _showWorkoutDetails(BuildContext context, Workout workout) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => WorkoutDetailsScreen(workout: workout),
      ),
    );
  }
}
