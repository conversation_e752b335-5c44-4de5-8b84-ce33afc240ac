import 'package:flutter/material.dart';
import '../../domain/models/user.dart';

/// Header widget for the dashboard with avatar, search, calendar, and classes button
class DashboardHeader extends StatelessWidget {
  const DashboardHeader({
    super.key,
    required this.user,
    this.onSearchTap,
    this.onCalendarTap,
    this.onClassesTap,
  });

  final User user;
  final VoidCallback? onSearchTap;
  final VoidCallback? onCalendarTap;
  final VoidCallback? onClassesTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Row(
        children: [
          // User Avatar
          _UserAvatar(user: user),
          
          const SizedBox(width: 16),
          
          // Search Icon
          IconButton(
            onPressed: onSearchTap,
            icon: Icon(
              Icons.search,
              color: theme.colorScheme.onSurface,
              size: 24,
            ),
          ),
          
          const SizedBox(width: 8),
          
          // Calendar Icon
          IconButton(
            onPressed: onCalendarTap,
            icon: Icon(
              Icons.calendar_today_outlined,
              color: theme.colorScheme.onSurface,
              size: 24,
            ),
          ),
          
          const Spacer(),
          
          // Classes Button
          _ClassesButton(onTap: onClassesTap),
        ],
      ),
    );
  }
}

/// User avatar widget with initials fallback
class _UserAvatar extends StatelessWidget {
  const _UserAvatar({required this.user});

  final User user;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: user.avatarUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Image.network(
                user.avatarUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _InitialsAvatar(user: user),
              ),
            )
          : _InitialsAvatar(user: user),
    );
  }
}

/// Avatar with user initials
class _InitialsAvatar extends StatelessWidget {
  const _InitialsAvatar({required this.user});

  final User user;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Text(
        user.initials,
        style: theme.textTheme.titleMedium?.copyWith(
          color: theme.colorScheme.primary,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

/// Classes button widget
class _ClassesButton extends StatelessWidget {
  const _ClassesButton({this.onTap});

  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: theme.colorScheme.outline.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Text(
            'Classes',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
