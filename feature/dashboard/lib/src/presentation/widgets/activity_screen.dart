import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/dashboard_bloc.dart';
import '../bloc/dashboard_state.dart';
import '../../domain/models/workout.dart';

/// Activity screen showing workout history, achievements, and statistics
class ActivityScreen extends StatelessWidget {
  const ActivityScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: BlocBuilder<DashboardBloc, DashboardState>(
          builder: (context, state) {
            if (state.isLoading) {
              return const _LoadingView();
            }

            return CustomScrollView(
              slivers: [
                // Header
                _ActivityHeader(),
                
                // Stats overview
                _StatsOverview(workouts: state.allWorkouts),
                
                // Weekly progress
                _WeeklyProgress(),
                
                // Recent activities
                _RecentActivities(workouts: state.allWorkouts),
                
                // Achievements
                _Achievements(),
              ],
            );
          },
        ),
      ),
    );
  }
}

/// Activity header
class _ActivityHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Activity',
              style: theme.textTheme.headlineLarge?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w700,
              ),
            ),
            
            const SizedBox(height: 8),
            
            Text(
              'Track your fitness journey',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Stats overview section
class _StatsOverview extends StatelessWidget {
  const _StatsOverview({required this.workouts});

  final List<Workout> workouts;

  @override
  Widget build(BuildContext context) {
    final completedWorkouts = workouts.where((w) => w.isCompleted).length;
    final totalMinutes = workouts
        .where((w) => w.isCompleted)
        .fold<int>(0, (sum, w) => sum + w.durationMinutes);
    final favoriteWorkouts = workouts.where((w) => w.isFavorite).length;
    
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'This Week',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _StatCard(
                    icon: Icons.fitness_center,
                    title: 'Workouts',
                    value: '$completedWorkouts',
                    subtitle: 'Completed',
                    color: const Color(0xFF4CAF50),
                  ),
                ),
                
                const SizedBox(width: 16),
                
                Expanded(
                  child: _StatCard(
                    icon: Icons.access_time,
                    title: 'Minutes',
                    value: '$totalMinutes',
                    subtitle: 'Active time',
                    color: const Color(0xFF2196F3),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _StatCard(
                    icon: Icons.favorite,
                    title: 'Favorites',
                    value: '$favoriteWorkouts',
                    subtitle: 'Saved workouts',
                    color: const Color(0xFFFF6B9D),
                  ),
                ),
                
                const SizedBox(width: 16),
                
                Expanded(
                  child: _StatCard(
                    icon: Icons.local_fire_department,
                    title: 'Streak',
                    value: '3',
                    subtitle: 'Days in a row',
                    color: const Color(0xFFFF9500),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}

/// Individual stat card
class _StatCard extends StatelessWidget {
  const _StatCard({
    required this.icon,
    required this.title,
    required this.value,
    required this.subtitle,
    required this.color,
  });

  final IconData icon;
  final String title;
  final String value;
  final String subtitle;
  final Color color;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: color,
              ),
              
              const SizedBox(width: 8),
              
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          Text(
            value,
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
          
          Text(
            subtitle,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ),
    );
  }
}

/// Weekly progress chart
class _WeeklyProgress extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Weekly Progress',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: 16),
            
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  // Progress bars for each day
                  _DayProgressBar(day: 'Mon', progress: 0.8, isToday: false),
                  _DayProgressBar(day: 'Tue', progress: 1.0, isToday: false),
                  _DayProgressBar(day: 'Wed', progress: 0.6, isToday: true),
                  _DayProgressBar(day: 'Thu', progress: 0.0, isToday: false),
                  _DayProgressBar(day: 'Fri', progress: 0.0, isToday: false),
                  _DayProgressBar(day: 'Sat', progress: 0.0, isToday: false),
                  _DayProgressBar(day: 'Sun', progress: 0.0, isToday: false),
                ],
              ),
            ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}

/// Individual day progress bar
class _DayProgressBar extends StatelessWidget {
  const _DayProgressBar({
    required this.day,
    required this.progress,
    required this.isToday,
  });

  final String day;
  final double progress;
  final bool isToday;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 40,
            child: Text(
              day,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: isToday ? FontWeight.w600 : FontWeight.w400,
                color: isToday 
                    ? theme.colorScheme.primary 
                    : theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: theme.colorScheme.outline.withOpacity(0.2),
              valueColor: AlwaysStoppedAnimation<Color>(
                isToday 
                    ? theme.colorScheme.primary
                    : const Color(0xFF4CAF50),
              ),
              minHeight: 6,
            ),
          ),
          
          const SizedBox(width: 12),
          
          SizedBox(
            width: 40,
            child: Text(
              '${(progress * 100).round()}%',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }
}

/// Recent activities section
class _RecentActivities extends StatelessWidget {
  const _RecentActivities({required this.workouts});

  final List<Workout> workouts;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final completedWorkouts = workouts.where((w) => w.isCompleted).toList();
    
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Recent Activities',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                
                const Spacer(),
                
                TextButton(
                  onPressed: () => _showAllActivities(context),
                  child: Text(
                    'See All',
                    style: TextStyle(
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            if (completedWorkouts.isEmpty)
              _EmptyActivities()
            else
              ...completedWorkouts.take(5).map((workout) => 
                _ActivityItem(workout: workout)
              ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  void _showAllActivities(BuildContext context) {
    // TODO: Navigate to all activities
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('All activities coming soon!')),
    );
  }
}

/// Individual activity item
class _ActivityItem extends StatelessWidget {
  const _ActivityItem({required this.workout});

  final Workout workout;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            // Completion indicator
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFF4CAF50),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check,
                color: Colors.white,
                size: 20,
              ),
            ),
            
            const SizedBox(width: 16),
            
            // Workout info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    workout.title,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  
                  const SizedBox(height: 4),
                  
                  Text(
                    '${workout.durationMinutes} mins • ${workout.category.name}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
            
            // Time ago
            Text(
              '2h ago',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Empty activities state
class _EmptyActivities extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            Icons.fitness_center,
            size: 48,
            color: theme.colorScheme.primary.withOpacity(0.5),
          ),
          
          const SizedBox(height: 16),
          
          Text(
            'No Activities Yet',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 8),
          
          Text(
            'Complete your first workout to see your activity history here.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Achievements section
class _Achievements extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Achievements',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _AchievementCard(
                    icon: Icons.local_fire_department,
                    title: 'First Workout',
                    description: 'Complete your first workout',
                    isUnlocked: true,
                    color: const Color(0xFFFF9500),
                  ),
                ),
                
                const SizedBox(width: 16),
                
                Expanded(
                  child: _AchievementCard(
                    icon: Icons.calendar_today,
                    title: '7 Day Streak',
                    description: 'Workout 7 days in a row',
                    isUnlocked: false,
                    color: const Color(0xFF2196F3),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 100), // Space for bottom navigation
          ],
        ),
      ),
    );
  }
}

/// Individual achievement card
class _AchievementCard extends StatelessWidget {
  const _AchievementCard({
    required this.icon,
    required this.title,
    required this.description,
    required this.isUnlocked,
    required this.color,
  });

  final IconData icon;
  final String title;
  final String description;
  final bool isUnlocked;
  final Color color;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
        border: isUnlocked
            ? Border.all(
                color: color.withOpacity(0.3),
                width: 1,
              )
            : null,
      ),
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: isUnlocked 
                  ? color.withOpacity(0.1)
                  : theme.colorScheme.outline.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 24,
              color: isUnlocked 
                  ? color
                  : theme.colorScheme.onSurface.withOpacity(0.4),
            ),
          ),
          
          const SizedBox(height: 12),
          
          Text(
            title,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: isUnlocked 
                  ? theme.colorScheme.onSurface
                  : theme.colorScheme.onSurface.withOpacity(0.6),
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 4),
          
          Text(
            description,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Loading view for activity screen
class _LoadingView extends StatelessWidget {
  const _LoadingView();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }
}
