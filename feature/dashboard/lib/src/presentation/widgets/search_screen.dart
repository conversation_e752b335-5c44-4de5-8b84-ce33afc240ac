import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/dashboard_bloc.dart';
import '../bloc/dashboard_event.dart';
import '../bloc/dashboard_state.dart';
import '../../domain/models/workout.dart';
import '../../domain/models/workout_category.dart';
import 'workout_card.dart';

/// Search screen for finding workouts with filters and categories
class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  WorkoutCategoryType? _selectedCategory;
  WorkoutDifficulty? _selectedDifficulty;

  @override
  void initState() {
    super.initState();
    _searchFocusNode.requestFocus();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    context.read<DashboardBloc>().add(WorkoutSearchRequested(query: query));
  }

  void _onCategorySelected(WorkoutCategoryType? category) {
    setState(() {
      _selectedCategory = category;
    });
    _applyFilters();
  }

  void _onDifficultySelected(WorkoutDifficulty? difficulty) {
    setState(() {
      _selectedDifficulty = difficulty;
    });
    _applyFilters();
  }

  void _applyFilters() {
    // For now, we'll use search with category name
    // In a real app, this would be a more sophisticated filter
    final query = _selectedCategory?.name ?? _searchController.text;
    context.read<DashboardBloc>().add(WorkoutSearchRequested(query: query));
  }

  void _clearFilters() {
    setState(() {
      _selectedCategory = null;
      _selectedDifficulty = null;
    });
    _searchController.clear();
    context.read<DashboardBloc>().add(const WorkoutFiltersCleared());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.arrow_back,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        title: Text(
          'Search Workouts',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          if (_selectedCategory != null || _selectedDifficulty != null || _searchController.text.isNotEmpty)
            TextButton(
              onPressed: _clearFilters,
              child: Text(
                'Clear',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          _SearchBar(
            controller: _searchController,
            focusNode: _searchFocusNode,
            onChanged: _onSearchChanged,
          ),

          // Filter Chips
          _FilterSection(
            selectedCategory: _selectedCategory,
            selectedDifficulty: _selectedDifficulty,
            onCategorySelected: _onCategorySelected,
            onDifficultySelected: _onDifficultySelected,
          ),

          // Search Results
          Expanded(
            child: BlocBuilder<DashboardBloc, DashboardState>(
              builder: (context, state) {
                if (state.isLoading) {
                  return const _LoadingView();
                }

                if (state.hasActiveFilters) {
                  return _SearchResults(workouts: state.filteredWorkouts);
                }

                return const _EmptySearchView();
              },
            ),
          ),
        ],
      ),
    );
  }
}

/// Search bar widget
class _SearchBar extends StatelessWidget {
  const _SearchBar({
    required this.controller,
    required this.focusNode,
    required this.onChanged,
  });

  final TextEditingController controller;
  final FocusNode focusNode;
  final ValueChanged<String> onChanged;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.all(20),
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        onChanged: onChanged,
        decoration: InputDecoration(
          hintText: 'Search workouts, instructors, categories...',
          hintStyle: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
          prefixIcon: Icon(
            Icons.search,
            color: theme.colorScheme.onSurface.withOpacity(0.6),
          ),
          suffixIcon: controller.text.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    controller.clear();
                    onChanged('');
                  },
                  icon: Icon(
                    Icons.clear,
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                  ),
                )
              : null,
          filled: true,
          fillColor: theme.colorScheme.surfaceContainerHighest,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),
    );
  }
}

/// Filter section with category and difficulty chips
class _FilterSection extends StatelessWidget {
  const _FilterSection({
    required this.selectedCategory,
    required this.selectedDifficulty,
    required this.onCategorySelected,
    required this.onDifficultySelected,
  });

  final WorkoutCategoryType? selectedCategory;
  final WorkoutDifficulty? selectedDifficulty;
  final ValueChanged<WorkoutCategoryType?> onCategorySelected;
  final ValueChanged<WorkoutDifficulty?> onDifficultySelected;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Category filters
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Text(
            'Categories',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        
        const SizedBox(height: 8),
        
        SizedBox(
          height: 40,
          child: ListView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 20),
            children: WorkoutCategoryType.values.map((category) {
              final isSelected = selectedCategory == category;
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: FilterChip(
                  label: Text(_getCategoryDisplayName(category)),
                  selected: isSelected,
                  onSelected: (selected) {
                    onCategorySelected(selected ? category : null);
                  },
                ),
              );
            }).toList(),
          ),
        ),

        const SizedBox(height: 16),

        // Difficulty filters
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Text(
            'Difficulty',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        
        const SizedBox(height: 8),
        
        SizedBox(
          height: 40,
          child: ListView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 20),
            children: WorkoutDifficulty.values.map((difficulty) {
              final isSelected = selectedDifficulty == difficulty;
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: FilterChip(
                  label: Text(_getDifficultyDisplayName(difficulty)),
                  selected: isSelected,
                  onSelected: (selected) {
                    onDifficultySelected(selected ? difficulty : null);
                  },
                ),
              );
            }).toList(),
          ),
        ),

        const SizedBox(height: 16),
      ],
    );
  }

  String _getCategoryDisplayName(WorkoutCategoryType category) {
    switch (category) {
      case WorkoutCategoryType.meditation:
        return 'Meditation';
      case WorkoutCategoryType.pilates:
        return 'Pilates';
      case WorkoutCategoryType.yoga:
        return 'Yoga';
      case WorkoutCategoryType.strength:
        return 'Strength';
      case WorkoutCategoryType.cardio:
        return 'Cardio';
      case WorkoutCategoryType.stretching:
        return 'Stretching';
    }
  }

  String _getDifficultyDisplayName(WorkoutDifficulty difficulty) {
    switch (difficulty) {
      case WorkoutDifficulty.beginner:
        return 'Beginner';
      case WorkoutDifficulty.intermediate:
        return 'Intermediate';
      case WorkoutDifficulty.advanced:
        return 'Advanced';
    }
  }
}

/// Search results list
class _SearchResults extends StatelessWidget {
  const _SearchResults({required this.workouts});

  final List<Workout> workouts;

  @override
  Widget build(BuildContext context) {
    if (workouts.isEmpty) {
      return const _NoResultsView();
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: workouts.length,
      itemBuilder: (context, index) {
        final workout = workouts[index];
        return WorkoutCard(
          workout: workout,
          onTap: () => _navigateToWorkoutDetails(context, workout),
          onFavoriteTap: () => context.read<DashboardBloc>().add(
            WorkoutFavoriteToggled(workoutId: workout.id),
          ),
        );
      },
    );
  }

  void _navigateToWorkoutDetails(BuildContext context, Workout workout) {
    // TODO: Navigate to workout details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Opening ${workout.title} details...')),
    );
  }
}

/// Loading view for search
class _LoadingView extends StatelessWidget {
  const _LoadingView();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }
}

/// Empty search view when no search is active
class _EmptySearchView extends StatelessWidget {
  const _EmptySearchView();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search,
              size: 64,
              color: theme.colorScheme.primary.withOpacity(0.5),
            ),
            
            const SizedBox(height: 16),
            
            Text(
              'Find Your Perfect Workout',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 8),
            
            Text(
              'Search by workout name, instructor, or category to discover new fitness experiences.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// No results view when search returns empty
class _NoResultsView extends StatelessWidget {
  const _NoResultsView();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
            
            const SizedBox(height: 16),
            
            Text(
              'No Results Found',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 8),
            
            Text(
              'Try adjusting your search terms or filters to find what you\'re looking for.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
