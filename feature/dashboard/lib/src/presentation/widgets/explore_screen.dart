import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/dashboard_bloc.dart';
import '../bloc/dashboard_event.dart';
import '../bloc/dashboard_state.dart';
import '../../domain/models/workout.dart';
import '../../domain/models/workout_category.dart';
import 'workout_card.dart';
import 'search_screen.dart';
import 'workout_details_screen.dart';

/// Explore screen for browsing workouts by categories and featured content
class ExploreScreen extends StatefulWidget {
  const ExploreScreen({super.key});

  @override
  State<ExploreScreen> createState() => _ExploreScreenState();
}

class _ExploreScreenState extends State<ExploreScreen> {
  @override
  void initState() {
    super.initState();
    // Load all workouts when screen initializes
    context.read<DashboardBloc>().add(const DashboardLoadRequested());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: BlocBuilder<DashboardBloc, DashboardState>(
          builder: (context, state) {
            if (state.isLoading) {
              return const _LoadingView();
            }

            return CustomScrollView(
              slivers: [
                // Header with search
                _ExploreHeader(),
                
                // Featured section
                _FeaturedSection(workouts: state.allWorkouts),
                
                // Categories section
                _CategoriesSection(),
                
                // All workouts section
                _AllWorkoutsSection(workouts: state.allWorkouts),
              ],
            );
          },
        ),
      ),
    );
  }
}

/// Explore header with search functionality
class _ExploreHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              'Explore',
              style: theme.textTheme.headlineLarge?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w700,
              ),
            ),
            
            const SizedBox(height: 8),
            
            Text(
              'Discover new workouts and challenges',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Search bar
            _SearchBar(),
          ],
        ),
      ),
    );
  }
}

/// Search bar widget
class _SearchBar extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _navigateToSearch(context),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Icon(
                Icons.search,
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
              
              const SizedBox(width: 12),
              
              Text(
                'Search workouts, instructors...',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToSearch(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SearchScreen(),
      ),
    );
  }
}

/// Featured workouts section
class _FeaturedSection extends StatelessWidget {
  const _FeaturedSection({required this.workouts});

  final List<Workout> workouts;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final featuredWorkouts = workouts.take(3).toList();
    
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Text(
                  'Featured Workouts',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                
                const Spacer(),
                
                TextButton(
                  onPressed: () => _showAllFeatured(context),
                  child: Text(
                    'See All',
                    style: TextStyle(
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemCount: featuredWorkouts.length,
              itemBuilder: (context, index) {
                final workout = featuredWorkouts[index];
                return Padding(
                  padding: const EdgeInsets.only(right: 16),
                  child: SizedBox(
                    width: 300,
                    child: WorkoutCard(
                      workout: workout,
                      onTap: () => _navigateToWorkoutDetails(context, workout),
                      onFavoriteTap: () => context.read<DashboardBloc>().add(
                        WorkoutFavoriteToggled(workoutId: workout.id),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  void _showAllFeatured(BuildContext context) {
    // TODO: Navigate to all featured workouts
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('All featured workouts coming soon!')),
    );
  }

  void _navigateToWorkoutDetails(BuildContext context, Workout workout) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => WorkoutDetailsScreen(workout: workout),
      ),
    );
  }
}

/// Categories section
class _CategoriesSection extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              'Browse by Category',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              itemCount: WorkoutCategoryType.values.length,
              itemBuilder: (context, index) {
                final category = WorkoutCategoryType.values[index];
                return Padding(
                  padding: const EdgeInsets.only(right: 16),
                  child: _CategoryCard(category: category),
                );
              },
            ),
          ),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }
}

/// Individual category card
class _CategoryCard extends StatelessWidget {
  const _CategoryCard({required this.category});

  final WorkoutCategoryType category;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _filterByCategory(context, category),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          width: 100,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: _getCategoryColor(category).withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: _getCategoryColor(category).withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _getCategoryIcon(category),
                size: 32,
                color: _getCategoryColor(category),
              ),
              
              const SizedBox(height: 8),
              
              Text(
                _getCategoryDisplayName(category),
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: _getCategoryColor(category),
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _filterByCategory(BuildContext context, WorkoutCategoryType category) {
    context.read<DashboardBloc>().add(
      WorkoutCategoryFilterRequested(categoryId: category.name),
    );
    
    // Navigate to search screen with category filter
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SearchScreen(),
      ),
    );
  }

  Color _getCategoryColor(WorkoutCategoryType category) {
    switch (category) {
      case WorkoutCategoryType.meditation:
        return const Color(0xFF6B73FF);
      case WorkoutCategoryType.pilates:
        return const Color(0xFFFF6B9D);
      case WorkoutCategoryType.yoga:
        return const Color(0xFF4ECDC4);
      case WorkoutCategoryType.strength:
        return const Color(0xFFFF9500);
      case WorkoutCategoryType.cardio:
        return const Color(0xFFFF3B30);
      case WorkoutCategoryType.stretching:
        return const Color(0xFF30D158);
    }
  }

  IconData _getCategoryIcon(WorkoutCategoryType category) {
    switch (category) {
      case WorkoutCategoryType.meditation:
        return Icons.self_improvement;
      case WorkoutCategoryType.pilates:
        return Icons.fitness_center;
      case WorkoutCategoryType.yoga:
        return Icons.spa;
      case WorkoutCategoryType.strength:
        return Icons.fitness_center;
      case WorkoutCategoryType.cardio:
        return Icons.directions_run;
      case WorkoutCategoryType.stretching:
        return Icons.accessibility_new;
    }
  }

  String _getCategoryDisplayName(WorkoutCategoryType category) {
    switch (category) {
      case WorkoutCategoryType.meditation:
        return 'Meditation';
      case WorkoutCategoryType.pilates:
        return 'Pilates';
      case WorkoutCategoryType.yoga:
        return 'Yoga';
      case WorkoutCategoryType.strength:
        return 'Strength';
      case WorkoutCategoryType.cardio:
        return 'Cardio';
      case WorkoutCategoryType.stretching:
        return 'Stretching';
    }
  }
}

/// All workouts section
class _AllWorkoutsSection extends StatelessWidget {
  const _AllWorkoutsSection({required this.workouts});

  final List<Workout> workouts;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Text(
                  'All Workouts',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                
                const Spacer(),
                
                Text(
                  '${workouts.length} workouts',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Workout grid
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.8,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: workouts.length,
              itemBuilder: (context, index) {
                final workout = workouts[index];
                return _WorkoutGridCard(workout: workout);
              },
            ),
          ),
          
          const SizedBox(height: 100), // Space for bottom navigation
        ],
      ),
    );
  }
}

/// Workout grid card for compact display
class _WorkoutGridCard extends StatelessWidget {
  const _WorkoutGridCard({required this.workout});

  final Workout workout;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _navigateToWorkoutDetails(context, workout),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Workout image placeholder
              Expanded(
                flex: 3,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: _getCategoryColor(workout.category.type).withOpacity(0.2),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                  ),
                  child: Center(
                    child: Icon(
                      _getCategoryIcon(workout.category.type),
                      size: 32,
                      color: _getCategoryColor(workout.category.type),
                    ),
                  ),
                ),
              ),
              
              // Workout info
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        workout.title,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: 4),
                      
                      Text(
                        '${workout.durationMinutes} mins • ${workout.difficultyDisplayName}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToWorkoutDetails(BuildContext context, Workout workout) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => WorkoutDetailsScreen(workout: workout),
      ),
    );
  }

  Color _getCategoryColor(WorkoutCategoryType category) {
    switch (category) {
      case WorkoutCategoryType.meditation:
        return const Color(0xFF6B73FF);
      case WorkoutCategoryType.pilates:
        return const Color(0xFFFF6B9D);
      case WorkoutCategoryType.yoga:
        return const Color(0xFF4ECDC4);
      case WorkoutCategoryType.strength:
        return const Color(0xFFFF9500);
      case WorkoutCategoryType.cardio:
        return const Color(0xFFFF3B30);
      case WorkoutCategoryType.stretching:
        return const Color(0xFF30D158);
    }
  }

  IconData _getCategoryIcon(WorkoutCategoryType category) {
    switch (category) {
      case WorkoutCategoryType.meditation:
        return Icons.self_improvement;
      case WorkoutCategoryType.pilates:
        return Icons.fitness_center;
      case WorkoutCategoryType.yoga:
        return Icons.spa;
      case WorkoutCategoryType.strength:
        return Icons.fitness_center;
      case WorkoutCategoryType.cardio:
        return Icons.directions_run;
      case WorkoutCategoryType.stretching:
        return Icons.accessibility_new;
    }
  }
}

/// Loading view for explore screen
class _LoadingView extends StatelessWidget {
  const _LoadingView();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }
}
