import 'package:flutter/material.dart';
import '../../domain/models/program.dart';
import '../../domain/models/workout.dart';
import 'workout_details_screen.dart';

/// Program details screen showing program overview and progress tracking
class ProgramDetailsScreen extends StatelessWidget {
  const ProgramDetailsScreen({
    super.key,
    required this.program,
  });

  final Program program;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: CustomScrollView(
        slivers: [
          // Program header
          _ProgramHeaderSection(program: program),
          
          // Program content
          SliverToBoxAdapter(
            child: _ProgramContent(program: program),
          ),
        ],
      ),
    );
  }
}

/// Program header section with progress
class _ProgramHeaderSection extends StatelessWidget {
  const _ProgramHeaderSection({required this.program});

  final Program program;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      backgroundColor: theme.colorScheme.surface,
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: Icon(
          Icons.arrow_back,
          color: theme.colorScheme.onSurface,
        ),
      ),
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          program.title,
          style: theme.textTheme.titleLarge?.copyWith(
            color: theme.colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.primary.withOpacity(0.1),
                theme.colorScheme.secondary.withOpacity(0.1),
              ],
            ),
          ),
          child: Center(
            child: _ProgramProgressCircle(program: program),
          ),
        ),
      ),
    );
  }
}

/// Circular progress indicator for program
class _ProgramProgressCircle extends StatelessWidget {
  const _ProgramProgressCircle({required this.program});

  final Program program;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progress = program.progressPercentage;
    
    return SizedBox(
      width: 120,
      height: 120,
      child: Stack(
        children: [
          // Background circle
          CircularProgressIndicator(
            value: 1.0,
            strokeWidth: 8,
            backgroundColor: theme.colorScheme.outline.withOpacity(0.2),
            valueColor: AlwaysStoppedAnimation<Color>(
              theme.colorScheme.outline.withOpacity(0.2),
            ),
          ),
          
          // Progress circle
          CircularProgressIndicator(
            value: progress,
            strokeWidth: 8,
            backgroundColor: Colors.transparent,
            valueColor: AlwaysStoppedAnimation<Color>(
              const Color(0xFF4CAF50),
            ),
          ),
          
          // Progress text
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '${(progress * 100).round()}%',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                
                Text(
                  'Complete',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Main program content
class _ProgramContent extends StatelessWidget {
  const _ProgramContent({required this.program});

  final Program program;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Program overview
          _ProgramOverview(program: program),
          
          const SizedBox(height: 24),
          
          // Progress stats
          _ProgressStats(program: program),
          
          const SizedBox(height: 24),
          
          // Program description
          if (program.description != null)
            _ProgramDescription(description: program.description!),
          
          const SizedBox(height: 24),
          
          // Workout sessions
          _WorkoutSessions(program: program),
        ],
      ),
    );
  }
}

/// Program overview section
class _ProgramOverview extends StatelessWidget {
  const _ProgramOverview({required this.program});

  final Program program;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Program Overview',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: _OverviewCard(
                icon: Icons.fitness_center,
                title: 'Sessions',
                value: '${program.completedSessions}/${program.totalSessions}',
                subtitle: 'Completed',
              ),
            ),
            
            const SizedBox(width: 16),
            
            Expanded(
              child: _OverviewCard(
                icon: Icons.calendar_today,
                title: 'Week',
                value: '${program.currentWeek}',
                subtitle: program.estimatedWeeks != null ? 'of ${program.estimatedWeeks}' : 'Current',
              ),
            ),
          ],
        ),
      ],
    );
  }
}

/// Individual overview card
class _OverviewCard extends StatelessWidget {
  const _OverviewCard({
    required this.icon,
    required this.title,
    required this.value,
    required this.subtitle,
  });

  final IconData icon;
  final String title;
  final String value;
  final String subtitle;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: theme.colorScheme.primary,
              ),
              
              const SizedBox(width: 8),
              
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          Text(
            value,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.w700,
            ),
          ),
          
          Text(
            subtitle,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ),
    );
  }
}

/// Progress statistics section
class _ProgressStats extends StatelessWidget {
  const _ProgressStats({required this.program});

  final Program program;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Progress Tracking',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 16),
        
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              // Progress bar
              Row(
                children: [
                  Text(
                    program.progressDisplayText,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  
                  const Spacer(),
                  
                  Text(
                    program.weekDisplayText,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Progress bar
              LinearProgressIndicator(
                value: program.progressPercentage,
                backgroundColor: theme.colorScheme.outline.withOpacity(0.2),
                valueColor: const AlwaysStoppedAnimation<Color>(
                  Color(0xFF4CAF50),
                ),
                minHeight: 6,
              ),
              
              const SizedBox(height: 12),
              
              // Next milestone
              if (!program.isCompleted)
                Row(
                  children: [
                    Icon(
                      Icons.flag_outlined,
                      size: 16,
                      color: theme.colorScheme.primary,
                    ),
                    
                    const SizedBox(width: 8),
                    
                    Text(
                      'Next: Complete ${program.totalSessions - program.completedSessions} more sessions',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ],
    );
  }
}

/// Program description section
class _ProgramDescription extends StatelessWidget {
  const _ProgramDescription({required this.description});

  final String description;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'About This Program',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Text(
          description,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.8),
            height: 1.5,
          ),
        ),
      ],
    );
  }
}

/// Workout sessions list
class _WorkoutSessions extends StatelessWidget {
  const _WorkoutSessions({required this.program});

  final Program program;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Workout Sessions',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const Spacer(),
            
            Text(
              '${program.workouts.length} workouts',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Workout list
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: program.workouts.length,
          itemBuilder: (context, index) {
            final workout = program.workouts[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: _WorkoutSessionCard(
                workout: workout,
                sessionNumber: index + 1,
                onTap: () => _navigateToWorkoutDetails(context, workout),
              ),
            );
          },
        ),
      ],
    );
  }

  void _navigateToWorkoutDetails(BuildContext context, Workout workout) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => WorkoutDetailsScreen(workout: workout),
      ),
    );
  }
}

/// Individual workout session card
class _WorkoutSessionCard extends StatelessWidget {
  const _WorkoutSessionCard({
    required this.workout,
    required this.sessionNumber,
    required this.onTap,
  });

  final Workout workout;
  final int sessionNumber;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12),
            border: workout.isCompleted
                ? Border.all(
                    color: const Color(0xFF4CAF50).withOpacity(0.3),
                    width: 1,
                  )
                : null,
          ),
          child: Row(
            children: [
              // Session number or completion indicator
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: workout.isCompleted
                      ? const Color(0xFF4CAF50)
                      : theme.colorScheme.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: workout.isCompleted
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 20,
                        )
                      : Text(
                          '$sessionNumber',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
              
              const SizedBox(width: 16),
              
              // Workout info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      workout.title,
                      style: theme.textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    
                    const SizedBox(height: 4),
                    
                    Row(
                      children: [
                        Text(
                          workout.category.name,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                        
                        const SizedBox(width: 8),
                        
                        Container(
                          width: 4,
                          height: 4,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.onSurface.withOpacity(0.4),
                            shape: BoxShape.circle,
                          ),
                        ),
                        
                        const SizedBox(width: 8),
                        
                        Text(
                          '${workout.durationMinutes} mins',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // Arrow icon
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurface.withOpacity(0.4),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
