import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/models/workout_category.dart';import '../bloc/dashboard_bloc.dart';
import '../bloc/dashboard_event.dart';
import '../../domain/models/workout.dart';

/// Workout details screen showing comprehensive workout information
class WorkoutDetailsScreen extends StatelessWidget {
  const WorkoutDetailsScreen({
    super.key,
    required this.workout,
  });

  final Workout workout;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: CustomScrollView(
        slivers: [
          // Hero image with app bar
          _WorkoutHeroSection(workout: workout),
          
          // Workout content
          SliverToBoxAdapter(
            child: _WorkoutContent(workout: workout),
          ),
        ],
      ),
      
      // Bottom action bar
      bottomNavigationBar: _BottomActionBar(workout: workout),
    );
  }
}

/// Hero section with workout image and app bar
class _WorkoutHeroSection extends StatelessWidget {
  const _WorkoutHeroSection({required this.workout});

  final Workout workout;

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 300,
      pinned: true,
      backgroundColor: Theme.of(context).colorScheme.surface,
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.3),
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
        ),
      ),
      actions: [
        IconButton(
          onPressed: () => context.read<DashboardBloc>().add(
            WorkoutFavoriteToggled(workoutId: workout.id),
          ),
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.3),
              shape: BoxShape.circle,
            ),
            child: Icon(
              workout.isFavorite ? Icons.favorite : Icons.favorite_border,
              color: workout.isFavorite ? Colors.red : Colors.white,
            ),
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            // Workout image or placeholder
            _WorkoutHeroImage(workout: workout),
            
            // Gradient overlay
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.7),
                  ],
                  stops: const [0.5, 1.0],
                ),
              ),
            ),
            
            // Availability badge
            Positioned(
              top: 100,
              left: 20,
              child: _AvailabilityBadge(workout: workout),
            ),
          ],
        ),
      ),
    );
  }
}

/// Workout hero image with placeholder
class _WorkoutHeroImage extends StatelessWidget {
  const _WorkoutHeroImage({required this.workout});

  final Workout workout;

  @override
  Widget build(BuildContext context) {
    if (workout.imageUrl != null) {
      return Image.asset(
        workout.imageUrl!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _PlaceholderImage(workout: workout),
      );
    }
    
    return _PlaceholderImage(workout: workout);
  }
}

/// Placeholder image for workouts without images
class _PlaceholderImage extends StatelessWidget {
  const _PlaceholderImage({required this.workout});

  final Workout workout;

  @override
  Widget build(BuildContext context) {
    // Different colors for different workout types
    final backgroundColor = switch (workout.category.type) {
      WorkoutCategoryType.meditation => const Color(0xFF6B73FF),
      WorkoutCategoryType.pilates => const Color(0xFFFF6B9D),
      WorkoutCategoryType.yoga => const Color(0xFF4ECDC4),
      WorkoutCategoryType.strength => const Color(0xFFFF9500),
      WorkoutCategoryType.cardio => const Color(0xFFFF3B30),
      WorkoutCategoryType.stretching => const Color(0xFF30D158),
    };
    
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            backgroundColor,
            backgroundColor.withOpacity(0.7),
          ],
        ),
      ),
      child: Center(
        child: Icon(
          _getWorkoutIcon(workout.category.type),
          size: 80,
          color: Colors.white.withOpacity(0.8),
        ),
      ),
    );
  }

  IconData _getWorkoutIcon(WorkoutCategoryType type) {
    switch (type) {
      case WorkoutCategoryType.meditation:
        return Icons.self_improvement;
      case WorkoutCategoryType.pilates:
        return Icons.fitness_center;
      case WorkoutCategoryType.yoga:
        return Icons.spa;
      case WorkoutCategoryType.strength:
        return Icons.fitness_center;
      case WorkoutCategoryType.cardio:
        return Icons.directions_run;
      case WorkoutCategoryType.stretching:
        return Icons.accessibility_new;
    }
  }
}

/// Availability badge
class _AvailabilityBadge extends StatelessWidget {
  const _AvailabilityBadge({required this.workout});

  final Workout workout;

  @override
  Widget build(BuildContext context) {
    return Transform.rotate(
      angle: -0.1,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Text(
          workout.availabilityDisplayName,
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
            fontSize: 11,
            letterSpacing: 0.5,
          ),
        ),
      ),
    );
  }
}

/// Main workout content
class _WorkoutContent extends StatelessWidget {
  const _WorkoutContent({required this.workout});

  final Workout workout;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Workout title
          Text(
            workout.title,
            style: theme.textTheme.headlineMedium?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.w700,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Workout metadata
          _WorkoutMetadata(workout: workout),
          
          const SizedBox(height: 24),
          
          // Instructor info
          if (workout.instructor != null)
            _InstructorInfo(instructor: workout.instructor!),
          
          const SizedBox(height: 24),
          
          // Description
          if (workout.description != null)
            _WorkoutDescription(description: workout.description!),
          
          const SizedBox(height: 24),
          
          // Workout stats
          _WorkoutStats(workout: workout),
          
          const SizedBox(height: 100), // Space for bottom action bar
        ],
      ),
    );
  }
}

/// Workout metadata (category, difficulty, duration)
class _WorkoutMetadata extends StatelessWidget {
  const _WorkoutMetadata({required this.workout});

  final Workout workout;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        _MetadataChip(
          icon: Icons.category_outlined,
          label: workout.category.name,
        ),
        
        const SizedBox(width: 8),
        
        _MetadataChip(
          icon: Icons.signal_cellular_alt,
          label: workout.difficultyDisplayName,
        ),
        
        const SizedBox(width: 8),
        
        _MetadataChip(
          icon: Icons.access_time,
          label: '${workout.durationMinutes} mins',
        ),
      ],
    );
  }
}

/// Individual metadata chip
class _MetadataChip extends StatelessWidget {
  const _MetadataChip({
    required this.icon,
    required this.label,
  });

  final IconData icon;
  final String label;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
          
          const SizedBox(width: 4),
          
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

/// Instructor information
class _InstructorInfo extends StatelessWidget {
  const _InstructorInfo({required this.instructor});

  final String instructor;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Instructor',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
              child: Text(
                instructor.split(' ').map((name) => name[0]).join(''),
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            
            const SizedBox(width: 12),
            
            Text(
              instructor,
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

/// Workout description
class _WorkoutDescription extends StatelessWidget {
  const _WorkoutDescription({required this.description});

  final String description;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'About This Workout',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Text(
          description,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withOpacity(0.8),
            height: 1.5,
          ),
        ),
      ],
    );
  }
}

/// Workout statistics
class _WorkoutStats extends StatelessWidget {
  const _WorkoutStats({required this.workout});

  final Workout workout;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Workout Details',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: _StatCard(
                icon: Icons.timer_outlined,
                label: 'Duration',
                value: '${workout.durationMinutes} min',
              ),
            ),
            
            const SizedBox(width: 16),
            
            Expanded(
              child: _StatCard(
                icon: Icons.local_fire_department_outlined,
                label: 'Intensity',
                value: workout.difficultyDisplayName,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

/// Individual stat card
class _StatCard extends StatelessWidget {
  const _StatCard({
    required this.icon,
    required this.label,
    required this.value,
  });

  final IconData icon;
  final String label;
  final String value;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 24,
            color: theme.colorScheme.primary,
          ),
          
          const SizedBox(height: 8),
          
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          
          const SizedBox(height: 4),
          
          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

/// Bottom action bar with play and bookmark buttons
class _BottomActionBar extends StatelessWidget {
  const _BottomActionBar({required this.workout});

  final Workout workout;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.2),
            width: 0.5,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Bookmark button
            IconButton(
              onPressed: () => context.read<DashboardBloc>().add(
                WorkoutFavoriteToggled(workoutId: workout.id),
              ),
              icon: Icon(
                workout.isFavorite ? Icons.bookmark : Icons.bookmark_border,
                color: workout.isFavorite ? theme.colorScheme.primary : theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            
            const SizedBox(width: 16),
            
            // Play button
            Expanded(
              child: FilledButton.icon(
                onPressed: () => _startWorkout(context),
                icon: const Icon(Icons.play_arrow),
                label: Text(
                  workout.isCompleted ? 'Play Again' : 'Start Workout',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: FilledButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _startWorkout(BuildContext context) {
    // TODO: Navigate to workout player or mark as completed
    context.read<DashboardBloc>().add(
      WorkoutCompletionToggled(workoutId: workout.id),
    );
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Starting ${workout.title}...'),
        action: SnackBarAction(
          label: 'Cancel',
          onPressed: () {},
        ),
      ),
    );
  }
}
