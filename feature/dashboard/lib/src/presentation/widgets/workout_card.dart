import 'package:flutter/material.dart';
import '../../domain/models/workout.dart';
import '../../domain/models/workout_category.dart';
/// Workout card widget displaying workout information
class WorkoutCard extends StatelessWidget {
  const WorkoutCard({
    super.key,
    required this.workout,
    this.onTap,
    this.onFavoriteTap,
  });

  final Workout workout;
  final VoidCallback? onTap;
  final VoidCallback? onFavoriteTap;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            height: 200,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: theme.colorScheme.surface,
            ),
            child: Stack(
              children: [
                // Background image
                _WorkoutImage(workout: workout),
                
                // Gradient overlay
                _GradientOverlay(),
                
                // Content
                _WorkoutContent(workout: workout),
                
                // Availability badge
                _AvailabilityBadge(workout: workout),
                
                // Favorite button
                if (onFavoriteTap != null)
                  _FavoriteButton(
                    workout: workout,
                    onTap: onFavoriteTap!,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Workout image with placeholder
class _WorkoutImage extends StatelessWidget {
  const _WorkoutImage({required this.workout});

  final Workout workout;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: workout.imageUrl != null
          ? Image.asset(
              workout.imageUrl!,
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => _PlaceholderImage(workout: workout),
            )
          : _PlaceholderImage(workout: workout),
    );
  }
}

/// Placeholder image for workouts without images
class _PlaceholderImage extends StatelessWidget {
  const _PlaceholderImage({required this.workout});

  final Workout workout;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Different colors for different workout types
    final backgroundColor = switch (workout.category.type) {
      WorkoutCategoryType.meditation => const Color(0xFF6B73FF),
      WorkoutCategoryType.pilates => const Color(0xFFFF6B9D),
      WorkoutCategoryType.yoga => const Color(0xFF4ECDC4),
      _ => theme.colorScheme.primary,
    };
    
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            backgroundColor,
            backgroundColor.withOpacity(0.7),
          ],
        ),
      ),
      child: Center(
        child: Icon(
          _getWorkoutIcon(workout.category.type),
          size: 48,
          color: Colors.white.withOpacity(0.8),
        ),
      ),
    );
  }

  IconData _getWorkoutIcon(WorkoutCategoryType type) {
    switch (type) {
      case WorkoutCategoryType.meditation:
        return Icons.self_improvement;
      case WorkoutCategoryType.pilates:
        return Icons.fitness_center;
      case WorkoutCategoryType.yoga:
        return Icons.spa;
      case WorkoutCategoryType.strength:
        return Icons.fitness_center;
      case WorkoutCategoryType.cardio:
        return Icons.directions_run;
      case WorkoutCategoryType.stretching:
        return Icons.accessibility_new;
    }
  }
}

/// Gradient overlay for better text readability
class _GradientOverlay extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withOpacity(0.7),
          ],
          stops: const [0.4, 1.0],
        ),
      ),
    );
  }
}

/// Workout content (title, category, duration, etc.)
class _WorkoutContent extends StatelessWidget {
  const _WorkoutContent({required this.workout});

  final Workout workout;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Positioned(
      left: 16,
      right: 16,
      bottom: 16,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Workout title
          Text(
            workout.title,
            style: theme.textTheme.titleMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: 8),
          
          // Workout metadata
          Row(
            children: [
              Text(
                workout.category.name,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
              
              const SizedBox(width: 8),
              
              Container(
                width: 4,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.6),
                  shape: BoxShape.circle,
                ),
              ),
              
              const SizedBox(width: 8),
              
              Text(
                workout.difficultyDisplayName,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
              
              const Spacer(),
              
              Text(
                '${workout.durationMinutes} mins',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// Availability badge (ON-DEMAND, LIVE, etc.)
class _AvailabilityBadge extends StatelessWidget {
  const _AvailabilityBadge({required this.workout});

  final Workout workout;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Positioned(
      top: 12,
      left: 12,
      child: Transform.rotate(
        angle: -0.1, // Slight rotation for design effect
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.7),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            workout.availabilityDisplayName,
            style: theme.textTheme.labelSmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
              fontSize: 10,
              letterSpacing: 0.5,
            ),
          ),
        ),
      ),
    );
  }
}

/// Favorite button
class _FavoriteButton extends StatelessWidget {
  const _FavoriteButton({
    required this.workout,
    required this.onTap,
  });

  final Workout workout;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 12,
      right: 12,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.3),
              shape: BoxShape.circle,
            ),
            child: Icon(
              workout.isFavorite ? Icons.favorite : Icons.favorite_border,
              color: workout.isFavorite ? Colors.red : Colors.white,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }
}
