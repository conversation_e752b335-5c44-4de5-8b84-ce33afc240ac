import 'package:equatable/equatable.dart';

/// User model representing the current user profile
class User extends Equatable {
  const User({
    required this.id,
    required this.name,
    required this.initials,
    this.avatarUrl,
  });

  /// User unique identifier
  final String id;
  
  /// User full name
  final String name;
  
  /// User initials for avatar fallback
  final String initials;
  
  /// Optional avatar image URL
  final String? avatarUrl;

  /// Creates a copy of this user with the given fields replaced
  User copyWith({
    String? id,
    String? name,
    String? initials,
    String? avatarUrl,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      initials: initials ?? this.initials,
      avatarUrl: avatarUrl ?? this.avatarUrl,
    );
  }

  @override
  List<Object?> get props => [id, name, initials, avatarUrl];

  @override
  String toString() => 'User(id: $id, name: $name, initials: $initials, avatarUrl: $avatarUrl)';
}
