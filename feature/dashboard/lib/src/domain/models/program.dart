import 'package:equatable/equatable.dart';
import 'workout.dart';

/// Program model representing a workout program with progress tracking
class Program extends Equatable {
  const Program({
    required this.id,
    required this.title,
    required this.totalSessions,
    required this.completedSessions,
    required this.currentWeek,
    required this.workouts,
    this.description,
    this.imageUrl,
    this.estimatedWeeks,
  });

  /// Program unique identifier
  final String id;
  
  /// Program title
  final String title;
  
  /// Total number of sessions in the program
  final int totalSessions;
  
  /// Number of completed sessions
  final int completedSessions;
  
  /// Current week number
  final int currentWeek;
  
  /// List of workouts in this program
  final List<Workout> workouts;
  
  /// Optional program description
  final String? description;
  
  /// Optional program image URL
  final String? imageUrl;
  
  /// Estimated number of weeks to complete
  final int? estimatedWeeks;

  /// Calculate progress percentage (0.0 to 1.0)
  double get progressPercentage {
    if (totalSessions == 0) return 0.0;
    return completedSessions / totalSessions;
  }

  /// Get progress as display string
  String get progressDisplayText {
    return '$completedSessions / $totalSessions Required Sessions';
  }

  /// Get week display text
  String get weekDisplayText {
    return 'Week $currentWeek';
  }

  /// Check if program is completed
  bool get isCompleted {
    return completedSessions >= totalSessions;
  }

  /// Creates a copy of this program with the given fields replaced
  Program copyWith({
    String? id,
    String? title,
    int? totalSessions,
    int? completedSessions,
    int? currentWeek,
    List<Workout>? workouts,
    String? description,
    String? imageUrl,
    int? estimatedWeeks,
  }) {
    return Program(
      id: id ?? this.id,
      title: title ?? this.title,
      totalSessions: totalSessions ?? this.totalSessions,
      completedSessions: completedSessions ?? this.completedSessions,
      currentWeek: currentWeek ?? this.currentWeek,
      workouts: workouts ?? this.workouts,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      estimatedWeeks: estimatedWeeks ?? this.estimatedWeeks,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        totalSessions,
        completedSessions,
        currentWeek,
        workouts,
        description,
        imageUrl,
        estimatedWeeks,
      ];

  @override
  String toString() => 'Program(id: $id, title: $title, progress: $completedSessions/$totalSessions)';
}
