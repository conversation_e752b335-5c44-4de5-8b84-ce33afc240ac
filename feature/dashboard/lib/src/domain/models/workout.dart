import 'package:equatable/equatable.dart';
import 'workout_category.dart';

/// Enum representing workout difficulty levels
enum WorkoutDifficulty {
  beginner,
  intermediate,
  advanced,
}

/// Enum representing workout availability
enum WorkoutAvailability {
  onDemand,
  scheduled,
  live,
}

/// Workout model representing individual workout sessions
class Workout extends Equatable {
  const Workout({
    required this.id,
    required this.title,
    required this.category,
    required this.difficulty,
    required this.durationMinutes,
    required this.availability,
    this.description,
    this.imageUrl,
    this.instructor,
    this.isCompleted = false,
    this.isFavorite = false,
  });

  /// Workout unique identifier
  final String id;
  
  /// Workout title
  final String title;
  
  /// Workout category
  final WorkoutCategory category;
  
  /// Workout difficulty level
  final WorkoutDifficulty difficulty;
  
  /// Duration in minutes
  final int durationMinutes;
  
  /// Workout availability type
  final WorkoutAvailability availability;
  
  /// Optional workout description
  final String? description;
  
  /// Optional workout image URL
  final String? imageUrl;
  
  /// Optional instructor name
  final String? instructor;
  
  /// Whether the workout has been completed
  final bool isCompleted;
  
  /// Whether the workout is marked as favorite
  final bool isFavorite;

  /// Creates a copy of this workout with the given fields replaced
  Workout copyWith({
    String? id,
    String? title,
    WorkoutCategory? category,
    WorkoutDifficulty? difficulty,
    int? durationMinutes,
    WorkoutAvailability? availability,
    String? description,
    String? imageUrl,
    String? instructor,
    bool? isCompleted,
    bool? isFavorite,
  }) {
    return Workout(
      id: id ?? this.id,
      title: title ?? this.title,
      category: category ?? this.category,
      difficulty: difficulty ?? this.difficulty,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      availability: availability ?? this.availability,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      instructor: instructor ?? this.instructor,
      isCompleted: isCompleted ?? this.isCompleted,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }

  /// Get difficulty as display string
  String get difficultyDisplayName {
    switch (difficulty) {
      case WorkoutDifficulty.beginner:
        return 'Beginner';
      case WorkoutDifficulty.intermediate:
        return 'Intermediate';
      case WorkoutDifficulty.advanced:
        return 'Advanced';
    }
  }

  /// Get availability as display string
  String get availabilityDisplayName {
    switch (availability) {
      case WorkoutAvailability.onDemand:
        return 'ON-DEMAND';
      case WorkoutAvailability.scheduled:
        return 'SCHEDULED';
      case WorkoutAvailability.live:
        return 'LIVE';
    }
  }

  @override
  List<Object?> get props => [
        id,
        title,
        category,
        difficulty,
        durationMinutes,
        availability,
        description,
        imageUrl,
        instructor,
        isCompleted,
        isFavorite,
      ];

  @override
  String toString() => 'Workout(id: $id, title: $title, category: ${category.name}, difficulty: $difficulty)';
}
