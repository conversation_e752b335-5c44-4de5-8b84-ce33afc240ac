import 'package:equatable/equatable.dart';

/// Enum representing different workout categories
enum WorkoutCategoryType {
  meditation,
  pilates,
  yoga,
  strength,
  cardio,
  stretching,
}

/// Workout category model
class WorkoutCategory extends Equatable {
  const WorkoutCategory({
    required this.id,
    required this.name,
    required this.type,
    this.description,
    this.iconPath,
  });

  /// Category unique identifier
  final String id;
  
  /// Category display name
  final String name;
  
  /// Category type enum
  final WorkoutCategoryType type;
  
  /// Optional category description
  final String? description;
  
  /// Optional icon asset path
  final String? iconPath;

  /// Creates a copy of this category with the given fields replaced
  WorkoutCategory copyWith({
    String? id,
    String? name,
    WorkoutCategoryType? type,
    String? description,
    String? iconPath,
  }) {
    return WorkoutCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      description: description ?? this.description,
      iconPath: iconPath ?? this.iconPath,
    );
  }

  @override
  List<Object?> get props => [id, name, type, description, iconPath];

  @override
  String toString() => 'WorkoutCategory(id: $id, name: $name, type: $type)';
}
