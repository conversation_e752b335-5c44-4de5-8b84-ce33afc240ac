import '../models/user.dart';
import '../models/program.dart';
import '../models/workout.dart';

/// Abstract repository interface for dashboard data
/// 
/// This interface defines the contract for dashboard data operations.
/// It can be implemented with different data sources (mock, API, local storage).
abstract interface class DashboardRepository {
  /// Get current user profile
  Future<User> getCurrentUser();

  /// Get user's current program with progress
  Future<Program?> getCurrentProgram();

  /// Get recommended workouts for today
  Future<List<Workout>> getTodayWorkouts();

  /// Get all available workouts
  Future<List<Workout>> getAllWorkouts();

  /// Mark workout as completed
  Future<void> markWorkoutCompleted(String workoutId);

  /// Toggle workout favorite status
  Future<void> toggleWorkoutFavorite(String workoutId);

  /// Update program progress
  Future<void> updateProgramProgress(String programId, int completedSessions);

  /// Search workouts by query
  Future<List<Workout>> searchWorkouts(String query);

  /// Get workouts by category
  Future<List<Workout>> getWorkoutsByCategory(String categoryId);
}
