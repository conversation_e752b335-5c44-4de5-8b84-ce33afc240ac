import '../../domain/models/user.dart';
import '../../domain/models/program.dart';
import '../../domain/models/workout.dart';
import '../../domain/repositories/dashboard_repository.dart';
import '../datasources/dashboard_mock_datasource.dart';

/// Implementation of DashboardRepository using mock data source
/// 
/// This implementation uses mock data but follows the same interface
/// that would be used with a real API. Easy to swap out later.
class DashboardRepositoryImpl implements DashboardRepository {
  const DashboardRepositoryImpl({
    required DashboardMockDataSource dataSource,
  }) : _dataSource = dataSource;

  final DashboardMockDataSource _dataSource;

  @override
  Future<User> getCurrentUser() async {
    try {
      return await _dataSource.getCurrentUser();
    } catch (e) {
      throw DashboardException('Failed to get current user: $e');
    }
  }

  @override
  Future<Program?> getCurrentProgram() async {
    try {
      return await _dataSource.getCurrentProgram();
    } catch (e) {
      throw DashboardException('Failed to get current program: $e');
    }
  }

  @override
  Future<List<Workout>> getTodayWorkouts() async {
    try {
      return await _dataSource.getTodayWorkouts();
    } catch (e) {
      throw DashboardException('Failed to get today workouts: $e');
    }
  }

  @override
  Future<List<Workout>> getAllWorkouts() async {
    try {
      return await _dataSource.getAllWorkouts();
    } catch (e) {
      throw DashboardException('Failed to get all workouts: $e');
    }
  }

  @override
  Future<void> markWorkoutCompleted(String workoutId) async {
    try {
      await _dataSource.markWorkoutCompleted(workoutId);
    } catch (e) {
      throw DashboardException('Failed to mark workout completed: $e');
    }
  }

  @override
  Future<void> toggleWorkoutFavorite(String workoutId) async {
    try {
      await _dataSource.toggleWorkoutFavorite(workoutId);
    } catch (e) {
      throw DashboardException('Failed to toggle workout favorite: $e');
    }
  }

  @override
  Future<void> updateProgramProgress(String programId, int completedSessions) async {
    try {
      // In real implementation, this would call the API
      await Future.delayed(const Duration(milliseconds: 200));
    } catch (e) {
      throw DashboardException('Failed to update program progress: $e');
    }
  }

  @override
  Future<List<Workout>> searchWorkouts(String query) async {
    try {
      return await _dataSource.searchWorkouts(query);
    } catch (e) {
      throw DashboardException('Failed to search workouts: $e');
    }
  }

  @override
  Future<List<Workout>> getWorkoutsByCategory(String categoryId) async {
    try {
      final allWorkouts = await _dataSource.getAllWorkouts();
      return allWorkouts.where((workout) => workout.category.id == categoryId).toList();
    } catch (e) {
      throw DashboardException('Failed to get workouts by category: $e');
    }
  }
}

/// Custom exception for dashboard operations
class DashboardException implements Exception {
  const DashboardException(this.message);
  
  final String message;
  
  @override
  String toString() => 'DashboardException: $message';
}
