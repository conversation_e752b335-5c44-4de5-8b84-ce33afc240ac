import '../../domain/models/user.dart';
import '../../domain/models/program.dart';
import '../../domain/models/workout.dart';
import '../../domain/models/workout_category.dart';

/// Mock data source for dashboard data
/// 
/// This provides realistic mock data that matches the design reference.
/// In production, this would be replaced with API calls.
class DashboardMockDataSource {
  // Mock categories
  static final _categories = [
    const WorkoutCategory(
      id: 'meditation',
      name: 'Meditation',
      type: WorkoutCategoryType.meditation,
      description: 'Mindfulness and meditation practices',
    ),
    const WorkoutCategory(
      id: 'pilates',
      name: 'Pilates',
      type: WorkoutCategoryType.pilates,
      description: 'Core strengthening and flexibility',
    ),
    const WorkoutCategory(
      id: 'yoga',
      name: 'Yoga',
      type: WorkoutCategoryType.yoga,
      description: 'Mind-body connection and flexibility',
    ),
  ];

  // Mock workouts matching the design reference
  static final _workouts = [
    Workout(
      id: 'meditation_1',
      title: 'Sonic Meditation: Peaceful Bowls',
      category: _categories[0], // Meditation
      difficulty: WorkoutDifficulty.beginner,
      durationMinutes: 5,
      availability: WorkoutAvailability.onDemand,
      description: 'A calming meditation session with singing bowls',
      imageUrl: 'assets/images/meditation_bowls.jpg',
      instructor: '<PERSON>',
    ),
    Workout(
      id: 'pilates_1',
      title: 'Pilates: Total Body Torcher',
      category: _categories[1], // Pilates
      difficulty: WorkoutDifficulty.intermediate,
      durationMinutes: 45,
      availability: WorkoutAvailability.onDemand,
      description: 'Full body pilates workout for strength and flexibility',
      imageUrl: 'assets/images/pilates_workout.jpg',
      instructor: 'Maria Rodriguez',
    ),
    Workout(
      id: 'yoga_1',
      title: 'Morning Flow Yoga',
      category: _categories[2], // Yoga
      difficulty: WorkoutDifficulty.beginner,
      durationMinutes: 20,
      availability: WorkoutAvailability.onDemand,
      description: 'Gentle morning yoga flow to start your day',
      imageUrl: 'assets/images/yoga_morning.jpg',
      instructor: 'Alex Chen',
    ),
  ];

  // Mock user
  static const _currentUser = User(
    id: 'user_1',
    name: 'Alex Smith',
    initials: 'AS',
    avatarUrl: null, // Will use initials
  );

  // Mock program
  static final _currentProgram = Program(
    id: 'program_1',
    title: 'YOUR PROGRAM',
    totalSessions: 4,
    completedSessions: 2,
    currentWeek: 1,
    workouts: _workouts,
    description: 'Beginner wellness program',
    estimatedWeeks: 4,
  );

  /// Get current user
  Future<User> getCurrentUser() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 300));
    return _currentUser;
  }

  /// Get current program
  Future<Program?> getCurrentProgram() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return _currentProgram;
  }

  /// Get today's recommended workouts
  Future<List<Workout>> getTodayWorkouts() async {
    await Future.delayed(const Duration(milliseconds: 400));
    // Return first 2 workouts as today's recommendations
    return _workouts.take(2).toList();
  }

  /// Get all workouts
  Future<List<Workout>> getAllWorkouts() async {
    await Future.delayed(const Duration(milliseconds: 600));
    return List.from(_workouts);
  }

  /// Mark workout as completed
  Future<void> markWorkoutCompleted(String workoutId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    // In real implementation, this would update the backend
    final index = _workouts.indexWhere((w) => w.id == workoutId);
    if (index != -1) {
      _workouts[index] = _workouts[index].copyWith(isCompleted: true);
    }
  }

  /// Toggle workout favorite
  Future<void> toggleWorkoutFavorite(String workoutId) async {
    await Future.delayed(const Duration(milliseconds: 200));
    final index = _workouts.indexWhere((w) => w.id == workoutId);
    if (index != -1) {
      _workouts[index] = _workouts[index].copyWith(
        isFavorite: !_workouts[index].isFavorite,
      );
    }
  }

  /// Search workouts
  Future<List<Workout>> searchWorkouts(String query) async {
    await Future.delayed(const Duration(milliseconds: 300));
    if (query.isEmpty) return _workouts;
    
    return _workouts.where((workout) =>
      workout.title.toLowerCase().contains(query.toLowerCase()) ||
      workout.category.name.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }
}
