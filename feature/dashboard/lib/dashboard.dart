/// Dashboard feature library
/// 
/// This library exports all the public APIs of the dashboard feature.
/// Other parts of the app should only import this file, not internal files.
library dashboard;

// Domain exports
export 'src/domain/models/user.dart';
export 'src/domain/models/program.dart';
export 'src/domain/models/workout.dart';
export 'src/domain/models/workout_category.dart';
export 'src/domain/repositories/dashboard_repository.dart';

// Data exports
export 'src/data/repositories/dashboard_repository_impl.dart';
export 'src/data/datasources/dashboard_mock_datasource.dart';

// Presentation exports
export 'src/presentation/bloc/dashboard_bloc.dart';
export 'src/presentation/bloc/dashboard_event.dart';
export 'src/presentation/bloc/dashboard_state.dart';
export 'src/presentation/widgets/dashboard_screen.dart';
export 'src/presentation/widgets/search_screen.dart';
export 'src/presentation/widgets/workout_details_screen.dart';
export 'src/presentation/widgets/program_details_screen.dart';
export 'src/presentation/widgets/explore_screen.dart';
export 'src/presentation/widgets/activity_screen.dart';
export 'src/presentation/widgets/more_screen.dart';export 'src/presentation/widgets/main_app_screen.dart';

// Injection
export 'src/injection.dart';
