import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'fitness_class.g.dart';

/// Fitness class model representing a workout class
/// Updated to match API requirement specification
@JsonSerializable()
class FitnessClass extends Equatable {
  const FitnessClass({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.startDate,
    required this.endDate,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Unique identifier for the class
  final String id;

  /// Class name/title
  final String name;

  /// Class description
  final String description;

  /// Category (Abs & Core, Arms & Shoulders, etc.)
  final String category;

  /// Start date and time of the class
  @JsonKey(name: 'start_date')
  final DateTime startDate;

  /// End date and time of the class
  @JsonKey(name: 'end_date')
  final DateTime endDate;

  /// Whether the class is currently active
  @JsonKey(name: 'is_active')
  final bool isActive;

  /// Created timestamp
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  /// Updated timestamp
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  // Computed properties for UI

  /// Get formatted duration text (calculated from start and end date)
  String get durationText {
    final duration = endDate.difference(startDate);
    final minutes = duration.inMinutes;
    if (minutes < 60) {
      return '${minutes} min';
    } else {
      final hours = duration.inHours;
      final remainingMinutes = minutes % 60;
      return remainingMinutes > 0 ? '${hours}h ${remainingMinutes}m' : '${hours}h';
    }
  }

  /// Get duration in minutes
  int get durationInMinutes => endDate.difference(startDate).inMinutes;

  /// Get formatted date text
  String get dateText {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final classDate = DateTime(startDate.year, startDate.month, startDate.day);

    if (classDate == today) {
      return 'Today';
    } else if (classDate == today.add(const Duration(days: 1))) {
      return 'Tomorrow';
    } else {
      return '${startDate.day}/${startDate.month}/${startDate.year}';
    }
  }

  /// Get formatted time text
  String get timeText {
    final startTime = '${startDate.hour.toString().padLeft(2, '0')}:${startDate.minute.toString().padLeft(2, '0')}';
    final endTime = '${endDate.hour.toString().padLeft(2, '0')}:${endDate.minute.toString().padLeft(2, '0')}';
    return '$startTime - $endTime';
  }

  /// Get category icon
  String get categoryIcon {
    switch (category.toLowerCase()) {
      case 'abs & core':
        return '💪';
      case 'arms & shoulders':
        return '💪';
      case 'glutes & legs':
        return '🦵';
      case 'full body':
        return '🏃';
      case 'cardio':
        return '❤️';
      case 'yoga':
        return '🧘';
      case 'strength':
        return '🏋️';
      default:
        return '💪';
    }
  }

  /// Creates a copy of this class with the given fields replaced
  FitnessClass copyWith({
    String? id,
    String? name,
    String? description,
    String? category,
    DateTime? startDate,
    DateTime? endDate,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FitnessClass(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Creates a FitnessClass from JSON
  factory FitnessClass.fromJson(Map<String, dynamic> json) => _$FitnessClassFromJson(json);

  /// Converts FitnessClass to JSON
  Map<String, dynamic> toJson() => _$FitnessClassToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        category,
        startDate,
        endDate,
        isActive,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() => 'FitnessClass(id: $id, name: $name, category: $category, duration: $durationText)';
}
