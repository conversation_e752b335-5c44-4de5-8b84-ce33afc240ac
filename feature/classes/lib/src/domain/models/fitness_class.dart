import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'fitness_class.g.dart';

/// Fitness class model representing a workout class
@JsonSerializable()
class FitnessClass extends Equatable {
  const FitnessClass({
    required this.id,
    required this.title,
    required this.description,
    required this.duration,
    required this.difficulty,
    required this.category,
    required this.equipment,
    required this.imageUrl,
    required this.instructorName,
    required this.createdAt,
    required this.updatedAt,
    this.tags = const [],
    this.isBookmarked = false,
    this.rating,
    this.totalRatings,
    this.caloriesBurn,
    this.muscleGroups = const [],
  });

  /// Unique identifier for the class
  final String id;
  
  /// Class title/name
  final String title;
  
  /// Class description
  final String description;
  
  /// Duration in minutes
  final int duration;
  
  /// Difficulty level (Beginner, Intermediate, Advanced)
  final String difficulty;
  
  /// Category (Abs & Core, Arms & Shoulders, etc.)
  final String category;
  
  /// Equipment required (No Equipment, Dumbbells, etc.)
  final String equipment;
  
  /// Image URL for the class
  @J<PERSON>Key(name: 'image_url')
  final String imageUrl;
  
  /// Instructor name
  @JsonKey(name: 'instructor_name')
  final String instructorName;
  
  /// Tags for the class
  final List<String> tags;
  
  /// Whether the class is bookmarked by user
  @JsonKey(name: 'is_bookmarked')
  final bool isBookmarked;
  
  /// Average rating (1-5)
  final double? rating;
  
  /// Total number of ratings
  @JsonKey(name: 'total_ratings')
  final int? totalRatings;
  
  /// Estimated calories burn
  @JsonKey(name: 'calories_burn')
  final int? caloriesBurn;
  
  /// Muscle groups targeted
  @JsonKey(name: 'muscle_groups')
  final List<String> muscleGroups;
  
  /// Created timestamp
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  
  /// Updated timestamp
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  // Computed properties for UI
  
  /// Get formatted duration text
  String get durationText => '${duration} min';
  
  /// Get difficulty color
  String get difficultyColor {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return '#4CAF50'; // Green
      case 'intermediate':
        return '#FF9800'; // Orange
      case 'advanced':
        return '#F44336'; // Red
      default:
        return '#9E9E9E'; // Grey
    }
  }
  
  /// Get formatted rating text
  String get ratingText {
    if (rating == null) return 'No ratings';
    return '${rating!.toStringAsFixed(1)} (${totalRatings ?? 0})';
  }
  
  /// Get formatted calories text
  String get caloriesText {
    if (caloriesBurn == null) return '';
    return '${caloriesBurn} cal';
  }
  
  /// Check if class requires equipment
  bool get requiresEquipment => equipment.toLowerCase() != 'no equipment';
  
  /// Get equipment icon
  String get equipmentIcon {
    switch (equipment.toLowerCase()) {
      case 'no equipment':
        return '🏃';
      case 'dumbbells':
        return '🏋️';
      case 'resistance bands':
        return '🎯';
      case 'yoga mat':
        return '🧘';
      default:
        return '💪';
    }
  }
  
  /// Get category icon
  String get categoryIcon {
    switch (category.toLowerCase()) {
      case 'abs & core':
        return '💪';
      case 'arms & shoulders':
        return '💪';
      case 'glutes & legs':
        return '🦵';
      case 'full body':
        return '🏃';
      case 'cardio':
        return '❤️';
      case 'yoga':
        return '🧘';
      case 'strength':
        return '🏋️';
      default:
        return '💪';
    }
  }

  /// Creates a copy of this class with the given fields replaced
  FitnessClass copyWith({
    String? id,
    String? title,
    String? description,
    int? duration,
    String? difficulty,
    String? category,
    String? equipment,
    String? imageUrl,
    String? instructorName,
    List<String>? tags,
    bool? isBookmarked,
    double? rating,
    int? totalRatings,
    int? caloriesBurn,
    List<String>? muscleGroups,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FitnessClass(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      duration: duration ?? this.duration,
      difficulty: difficulty ?? this.difficulty,
      category: category ?? this.category,
      equipment: equipment ?? this.equipment,
      imageUrl: imageUrl ?? this.imageUrl,
      instructorName: instructorName ?? this.instructorName,
      tags: tags ?? this.tags,
      isBookmarked: isBookmarked ?? this.isBookmarked,
      rating: rating ?? this.rating,
      totalRatings: totalRatings ?? this.totalRatings,
      caloriesBurn: caloriesBurn ?? this.caloriesBurn,
      muscleGroups: muscleGroups ?? this.muscleGroups,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Creates a FitnessClass from JSON
  factory FitnessClass.fromJson(Map<String, dynamic> json) => _$FitnessClassFromJson(json);

  /// Converts FitnessClass to JSON
  Map<String, dynamic> toJson() => _$FitnessClassToJson(this);

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        duration,
        difficulty,
        category,
        equipment,
        imageUrl,
        instructorName,
        tags,
        isBookmarked,
        rating,
        totalRatings,
        caloriesBurn,
        muscleGroups,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() => 'FitnessClass(id: $id, title: $title, category: $category, duration: ${duration}min)';
}
