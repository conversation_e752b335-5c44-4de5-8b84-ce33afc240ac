import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'class_category.g.dart';

/// Class category model for organizing fitness classes
@JsonSerializable()
class ClassCategory extends Equatable {
  const ClassCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.isActive,
    this.classCount = 0,
    this.imageUrl,
  });

  /// Unique identifier for the category
  final String id;
  
  /// Category name (e.g., "Abs & Core", "Arms & Shoulders")
  final String name;
  
  /// Category description
  final String description;
  
  /// Icon for the category
  final String icon;
  
  /// Color for the category (hex string)
  final String color;
  
  /// Whether the category is active
  @JsonKey(name: 'is_active')
  final bool isActive;
  
  /// Number of classes in this category
  @JsonKey(name: 'class_count')
  final int classCount;
  
  /// Optional image URL for the category
  @JsonKey(name: 'image_url')
  final String? imageUrl;

  /// Get formatted class count text
  String get classCountText {
    if (classCount == 0) return 'No classes';
    if (classCount == 1) return '1 class';
    return '$classCount classes';
  }

  /// Creates a copy of this category with the given fields replaced
  ClassCategory copyWith({
    String? id,
    String? name,
    String? description,
    String? icon,
    String? color,
    bool? isActive,
    int? classCount,
    String? imageUrl,
  }) {
    return ClassCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      classCount: classCount ?? this.classCount,
      imageUrl: imageUrl ?? this.imageUrl,
    );
  }

  /// Creates a ClassCategory from JSON
  factory ClassCategory.fromJson(Map<String, dynamic> json) => _$ClassCategoryFromJson(json);

  /// Converts ClassCategory to JSON
  Map<String, dynamic> toJson() => _$ClassCategoryToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        icon,
        color,
        isActive,
        classCount,
        imageUrl,
      ];

  @override
  String toString() => 'ClassCategory(id: $id, name: $name, classCount: $classCount)';
}
