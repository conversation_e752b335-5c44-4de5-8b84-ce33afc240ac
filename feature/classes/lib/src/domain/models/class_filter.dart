import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'class_filter.g.dart';

/// Filter options for fitness classes
@JsonSerializable()
class ClassFilter extends Equatable {
  const ClassFilter({
    this.categories = const [],
    this.difficulties = const [],
    this.durations = const [],
    this.equipment = const [],
    this.sortBy = ClassSortBy.newest,
    this.sortOrder = SortOrder.descending,
    this.searchQuery,
    this.minRating,
    this.maxDuration,
    this.minDuration,
    this.requiresNoEquipment = false,
  });

  /// Selected categories to filter by
  final List<String> categories;
  
  /// Selected difficulty levels
  final List<String> difficulties;
  
  /// Selected duration ranges
  final List<String> durations;
  
  /// Selected equipment types
  final List<String> equipment;
  
  /// Sort criteria
  @JsonKey(name: 'sort_by')
  final ClassSortBy sortBy;
  
  /// Sort order
  @JsonKey(name: 'sort_order')
  final SortOrder sortOrder;
  
  /// Search query text
  @<PERSON>son<PERSON>ey(name: 'search_query')
  final String? searchQuery;
  
  /// Minimum rating filter
  @JsonKey(name: 'min_rating')
  final double? minRating;
  
  /// Maximum duration filter (in minutes)
  @JsonKey(name: 'max_duration')
  final int? maxDuration;
  
  /// Minimum duration filter (in minutes)
  @JsonKey(name: 'min_duration')
  final int? minDuration;
  
  /// Filter for classes that require no equipment
  @JsonKey(name: 'requires_no_equipment')
  final bool requiresNoEquipment;

  /// Check if any filters are applied
  bool get hasActiveFilters {
    return categories.isNotEmpty ||
           difficulties.isNotEmpty ||
           durations.isNotEmpty ||
           equipment.isNotEmpty ||
           searchQuery?.isNotEmpty == true ||
           minRating != null ||
           maxDuration != null ||
           minDuration != null ||
           requiresNoEquipment ||
           sortBy != ClassSortBy.newest;
  }

  /// Get active filter count
  int get activeFilterCount {
    int count = 0;
    if (categories.isNotEmpty) count++;
    if (difficulties.isNotEmpty) count++;
    if (durations.isNotEmpty) count++;
    if (equipment.isNotEmpty) count++;
    if (searchQuery?.isNotEmpty == true) count++;
    if (minRating != null) count++;
    if (maxDuration != null) count++;
    if (minDuration != null) count++;
    if (requiresNoEquipment) count++;
    if (sortBy != ClassSortBy.newest) count++;
    return count;
  }

  /// Clear all filters
  ClassFilter clearAll() {
    return const ClassFilter();
  }

  /// Creates a copy of this filter with the given fields replaced
  ClassFilter copyWith({
    List<String>? categories,
    List<String>? difficulties,
    List<String>? durations,
    List<String>? equipment,
    ClassSortBy? sortBy,
    SortOrder? sortOrder,
    String? searchQuery,
    double? minRating,
    int? maxDuration,
    int? minDuration,
    bool? requiresNoEquipment,
  }) {
    return ClassFilter(
      categories: categories ?? this.categories,
      difficulties: difficulties ?? this.difficulties,
      durations: durations ?? this.durations,
      equipment: equipment ?? this.equipment,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
      searchQuery: searchQuery ?? this.searchQuery,
      minRating: minRating ?? this.minRating,
      maxDuration: maxDuration ?? this.maxDuration,
      minDuration: minDuration ?? this.minDuration,
      requiresNoEquipment: requiresNoEquipment ?? this.requiresNoEquipment,
    );
  }

  /// Creates a ClassFilter from JSON
  factory ClassFilter.fromJson(Map<String, dynamic> json) => _$ClassFilterFromJson(json);

  /// Converts ClassFilter to JSON
  Map<String, dynamic> toJson() => _$ClassFilterToJson(this);

  @override
  List<Object?> get props => [
        categories,
        difficulties,
        durations,
        equipment,
        sortBy,
        sortOrder,
        searchQuery,
        minRating,
        maxDuration,
        minDuration,
        requiresNoEquipment,
      ];

  @override
  String toString() => 'ClassFilter(categories: $categories, difficulties: $difficulties, sortBy: $sortBy)';
}

/// Sort criteria for classes
enum ClassSortBy {
  @JsonValue('newest')
  newest,
  @JsonValue('oldest')
  oldest,
  @JsonValue('duration_asc')
  durationAsc,
  @JsonValue('duration_desc')
  durationDesc,
  @JsonValue('rating')
  rating,
  @JsonValue('popularity')
  popularity,
  @JsonValue('alphabetical')
  alphabetical,
}

/// Sort order
enum SortOrder {
  @JsonValue('asc')
  ascending,
  @JsonValue('desc')
  descending,
}

/// Extension for ClassSortBy display names
extension ClassSortByExtension on ClassSortBy {
  String get displayName {
    switch (this) {
      case ClassSortBy.newest:
        return 'Newest';
      case ClassSortBy.oldest:
        return 'Oldest';
      case ClassSortBy.durationAsc:
        return 'Duration (Short to Long)';
      case ClassSortBy.durationDesc:
        return 'Duration (Long to Short)';
      case ClassSortBy.rating:
        return 'Highest Rated';
      case ClassSortBy.popularity:
        return 'Most Popular';
      case ClassSortBy.alphabetical:
        return 'A-Z';
    }
  }
}
