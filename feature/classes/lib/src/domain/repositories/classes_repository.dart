import '../models/fitness_class.dart';
import '../models/class_category.dart';
import '../models/class_filter.dart';

/// Repository interface for classes data operations
/// 
/// This interface defines the contract for classes data access.
/// It can be implemented by different data sources (API, local database, mock).
abstract class ClassesRepository {
  /// Get all available class categories
  Future<List<ClassCategory>> getCategories();

  /// Get classes by category with optional filtering
  Future<List<FitnessClass>> getClassesByCategory({
    required String categoryId,
    ClassFilter? filter,
    int? limit,
    int? offset,
  });

  /// Get all classes with optional filtering
  Future<List<FitnessClass>> getAllClasses({
    ClassFilter? filter,
    int? limit,
    int? offset,
  });

  /// Search classes by query
  Future<List<FitnessClass>> searchClasses({
    required String query,
    ClassFilter? filter,
    int? limit,
    int? offset,
  });

  /// Get class by ID
  Future<FitnessClass?> getClassById(String classId);

  /// Get featured/recommended classes
  Future<List<FitnessClass>> getFeaturedClasses({
    int? limit,
  });

  /// Get popular classes
  Future<List<FitnessClass>> getPopularClasses({
    int? limit,
  });

  /// Toggle bookmark status for a class
  Future<void> toggleBookmark(String classId, bool isBookmarked);

  /// Get bookmarked classes
  Future<List<FitnessClass>> getBookmarkedClasses();

  /// Rate a class
  Future<void> rateClass(String classId, double rating);

  /// Get classes by instructor
  Future<List<FitnessClass>> getClassesByInstructor({
    required String instructorId,
    int? limit,
    int? offset,
  });

  /// Get classes by difficulty level
  Future<List<FitnessClass>> getClassesByDifficulty({
    required String difficulty,
    int? limit,
    int? offset,
  });

  /// Get classes by duration range
  Future<List<FitnessClass>> getClassesByDuration({
    required int minDuration,
    required int maxDuration,
    int? limit,
    int? offset,
  });

  /// Get classes that require no equipment
  Future<List<FitnessClass>> getNoEquipmentClasses({
    int? limit,
    int? offset,
  });

  /// Get recently added classes
  Future<List<FitnessClass>> getRecentClasses({
    int? limit,
  });

  /// Get class statistics
  Future<Map<String, dynamic>> getClassStats();
}
