import '../../domain/models/fitness_class.dart';
import '../../domain/models/class_category.dart';
import '../../domain/models/class_filter.dart';
import '../../domain/repositories/classes_repository.dart';
import '../datasources/classes_api_datasource.dart';
import '../datasources/classes_mock_datasource.dart';

/// Implementation of ClassesRepository
/// 
/// This implementation can switch between mock and API data sources
/// based on configuration, making it easy to develop with mock data
/// and switch to real API when ready.
class ClassesRepositoryImpl implements ClassesRepository {
  const ClassesRepositoryImpl({
    required ClassesApiDataSource apiDataSource,
    required ClassesMockDataSource mockDataSource,
    this.useMockData = true, // Easy switch between mock and API
  }) : _apiDataSource = apiDataSource,
       _mockDataSource = mockDataSource;

  final ClassesApiDataSource _apiDataSource;
  final ClassesMockDataSource _mockDataSource;
  final bool useMockData;

  /// Get the appropriate data source based on configuration
  dynamic get _dataSource => useMockData ? _mockDataSource : _apiDataSource;

  @override
  Future<List<ClassCategory>> getCategories() async {
    try {
      print('📚 Fetching categories from ${useMockData ? 'mock' : 'API'} data source');
      return await _dataSource.getCategories();
    } catch (e) {
      print('❌ Error fetching categories: $e');
      // Fallback to mock data if API fails
      if (!useMockData) {
        print('🔄 Falling back to mock data');
        return await _mockDataSource.getCategories();
      }
      rethrow;
    }
  }

  @override
  Future<List<FitnessClass>> getClassesByCategory({
    required String categoryId,
    ClassFilter? filter,
    int? limit,
    int? offset,
  }) async {
    try {
      print('📚 Fetching classes by category from ${useMockData ? 'mock' : 'API'} data source');
      return await _dataSource.getClassesByCategory(
        categoryId: categoryId,
        filter: filter,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      print('❌ Error fetching classes by category: $e');
      // Fallback to mock data if API fails
      if (!useMockData) {
        print('🔄 Falling back to mock data');
        return await _mockDataSource.getClassesByCategory(
          categoryId: categoryId,
          filter: filter,
          limit: limit,
          offset: offset,
        );
      }
      rethrow;
    }
  }

  @override
  Future<List<FitnessClass>> getAllClasses({
    ClassFilter? filter,
    int? limit,
    int? offset,
  }) async {
    try {
      print('📚 Fetching all classes from ${useMockData ? 'mock' : 'API'} data source');
      return await _dataSource.getAllClasses(
        filter: filter,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      print('❌ Error fetching all classes: $e');
      // Fallback to mock data if API fails
      if (!useMockData) {
        print('🔄 Falling back to mock data');
        return await _mockDataSource.getAllClasses(
          filter: filter,
          limit: limit,
          offset: offset,
        );
      }
      rethrow;
    }
  }

  @override
  Future<List<FitnessClass>> searchClasses({
    required String query,
    ClassFilter? filter,
    int? limit,
    int? offset,
  }) async {
    try {
      print('🔍 Searching classes from ${useMockData ? 'mock' : 'API'} data source');
      return await _dataSource.searchClasses(
        query: query,
        filter: filter,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      print('❌ Error searching classes: $e');
      // Fallback to mock data if API fails
      if (!useMockData) {
        print('🔄 Falling back to mock data');
        return await _mockDataSource.searchClasses(
          query: query,
          filter: filter,
          limit: limit,
          offset: offset,
        );
      }
      rethrow;
    }
  }

  @override
  Future<FitnessClass?> getClassById(String classId) async {
    try {
      print('🔍 Fetching class by ID from ${useMockData ? 'mock' : 'API'} data source');
      return await _dataSource.getClassById(classId);
    } catch (e) {
      print('❌ Error fetching class by ID: $e');
      // Fallback to mock data if API fails
      if (!useMockData) {
        print('🔄 Falling back to mock data');
        return await _mockDataSource.getClassById(classId);
      }
      rethrow;
    }
  }

  @override
  Future<List<FitnessClass>> getFeaturedClasses({int? limit}) async {
    try {
      print('⭐ Fetching featured classes from ${useMockData ? 'mock' : 'API'} data source');
      return await _dataSource.getFeaturedClasses(limit: limit);
    } catch (e) {
      print('❌ Error fetching featured classes: $e');
      // Fallback to mock data if API fails
      if (!useMockData) {
        print('🔄 Falling back to mock data');
        return await _mockDataSource.getFeaturedClasses(limit: limit);
      }
      rethrow;
    }
  }

  @override
  Future<List<FitnessClass>> getPopularClasses({int? limit}) async {
    // For now, use featured classes as popular classes
    return getFeaturedClasses(limit: limit);
  }

  @override
  Future<void> toggleBookmark(String classId, bool isBookmarked) async {
    try {
      print('📝 Toggling bookmark from ${useMockData ? 'mock' : 'API'} data source');
      return await _dataSource.toggleBookmark(classId, isBookmarked);
    } catch (e) {
      print('❌ Error toggling bookmark: $e');
      // Fallback to mock data if API fails
      if (!useMockData) {
        print('🔄 Falling back to mock data');
        return await _mockDataSource.toggleBookmark(classId, isBookmarked);
      }
      rethrow;
    }
  }

  @override
  Future<List<FitnessClass>> getBookmarkedClasses() async {
    // Get all classes and filter bookmarked ones
    final allClasses = await getAllClasses();
    return allClasses.where((cls) => cls.isBookmarked).toList();
  }

  @override
  Future<void> rateClass(String classId, double rating) async {
    try {
      print('⭐ Rating class from ${useMockData ? 'mock' : 'API'} data source');
      if (useMockData) {
        // Mock implementation - just log the rating
        print('📝 Mock: Rated class $classId with $rating stars');
        return;
      } else {
        return await _apiDataSource.rateClass(classId, rating);
      }
    } catch (e) {
      print('❌ Error rating class: $e');
      rethrow;
    }
  }

  @override
  Future<List<FitnessClass>> getClassesByInstructor({
    required String instructorId,
    int? limit,
    int? offset,
  }) async {
    // Get all classes and filter by instructor
    final allClasses = await getAllClasses();
    var instructorClasses = allClasses.where((cls) => 
      cls.instructorName.toLowerCase().contains(instructorId.toLowerCase())).toList();
    
    // Apply pagination
    if (offset != null) {
      instructorClasses = instructorClasses.skip(offset).toList();
    }
    if (limit != null) {
      instructorClasses = instructorClasses.take(limit).toList();
    }
    
    return instructorClasses;
  }

  @override
  Future<List<FitnessClass>> getClassesByDifficulty({
    required String difficulty,
    int? limit,
    int? offset,
  }) async {
    final filter = ClassFilter(difficulties: [difficulty]);
    return getAllClasses(filter: filter, limit: limit, offset: offset);
  }

  @override
  Future<List<FitnessClass>> getClassesByDuration({
    required int minDuration,
    required int maxDuration,
    int? limit,
    int? offset,
  }) async {
    final filter = ClassFilter(
      minDuration: minDuration,
      maxDuration: maxDuration,
    );
    return getAllClasses(filter: filter, limit: limit, offset: offset);
  }

  @override
  Future<List<FitnessClass>> getNoEquipmentClasses({
    int? limit,
    int? offset,
  }) async {
    final filter = const ClassFilter(requiresNoEquipment: true);
    return getAllClasses(filter: filter, limit: limit, offset: offset);
  }

  @override
  Future<List<FitnessClass>> getRecentClasses({int? limit}) async {
    final filter = const ClassFilter(sortBy: ClassSortBy.newest);
    return getAllClasses(filter: filter, limit: limit);
  }

  @override
  Future<Map<String, dynamic>> getClassStats() async {
    try {
      final allClasses = await getAllClasses();
      final categories = await getCategories();
      
      return {
        'totalClasses': allClasses.length,
        'totalCategories': categories.length,
        'averageRating': allClasses
            .where((cls) => cls.rating != null)
            .map((cls) => cls.rating!)
            .fold(0.0, (sum, rating) => sum + rating) / 
            allClasses.where((cls) => cls.rating != null).length,
        'totalDuration': allClasses
            .map((cls) => cls.duration)
            .fold(0, (sum, duration) => sum + duration),
        'difficultyBreakdown': {
          'Beginner': allClasses.where((cls) => cls.difficulty == 'Beginner').length,
          'Intermediate': allClasses.where((cls) => cls.difficulty == 'Intermediate').length,
          'Advanced': allClasses.where((cls) => cls.difficulty == 'Advanced').length,
        },
        'categoryBreakdown': Map.fromEntries(
          categories.map((cat) => MapEntry(
            cat.name,
            allClasses.where((cls) => cls.category == cat.name).length,
          )),
        ),
      };
    } catch (e) {
      print('❌ Error fetching class stats: $e');
      return {};
    }
  }
}
