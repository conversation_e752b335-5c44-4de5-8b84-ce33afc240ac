import '../../domain/models/fitness_class.dart';
import '../../domain/models/class_category.dart';
import '../../domain/models/class_filter.dart';

/// Mock data source for classes - provides realistic fitness class data
/// This will be easily replaceable with real API data source
class ClassesMockDataSource {
  static final ClassesMockDataSource _instance = ClassesMockDataSource._internal();
  factory ClassesMockDataSource() => _instance;
  ClassesMockDataSource._internal();

  // Mock categories data
  static final List<ClassCategory> _mockCategories = [
    ClassCategory(
      id: 'abs-core',
      name: 'Abs & Core',
      description: 'Strengthen your core with targeted exercises',
      icon: '💪',
      color: '#FF6B6B',
      isActive: true,
      classCount: 23,
    ),
    ClassCategory(
      id: 'arms-shoulders',
      name: 'Arms & Shoulders',
      description: 'Build upper body strength and definition',
      icon: '💪',
      color: '#4ECDC4',
      isActive: true,
      classCount: 18,
    ),
    ClassCategory(
      id: 'glutes-legs',
      name: 'Glut<PERSON> & Legs',
      description: 'Tone and strengthen your lower body',
      icon: '🦵',
      color: '#45B7D1',
      isActive: true,
      classCount: 31,
    ),
    ClassCategory(
      id: 'full-body',
      name: 'Full Body',
      description: 'Complete workouts targeting all muscle groups',
      icon: '🏃',
      color: '#96CEB4',
      isActive: true,
      classCount: 15,
    ),
    ClassCategory(
      id: 'cardio',
      name: 'Cardio',
      description: 'High-intensity cardiovascular workouts',
      icon: '❤️',
      color: '#FFEAA7',
      isActive: true,
      classCount: 27,
    ),
    ClassCategory(
      id: 'yoga',
      name: 'Yoga',
      description: 'Flexibility, balance, and mindfulness',
      icon: '🧘',
      color: '#DDA0DD',
      isActive: true,
      classCount: 12,
    ),
  ];

  // Mock classes data - matching the design from the image
  static final List<FitnessClass> _mockClasses = [
    // Abs & Core classes
    FitnessClass(
      id: 'abs-1',
      title: '5-Min Bodyweight Burn: Core Wake-Up Call',
      description: 'Quick and effective core workout to start your day with energy and strength.',
      duration: 6,
      difficulty: 'Beginner',
      category: 'Abs & Core',
      equipment: 'No Equipment',
      imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
      instructorName: 'Sarah Johnson',
      tags: ['Endurance', 'Morning', 'Quick'],
      rating: 4.8,
      totalRatings: 1247,
      caloriesBurn: 45,
      muscleGroups: ['Core', 'Abs'],
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    FitnessClass(
      id: 'abs-2',
      title: 'Core Strength',
      description: 'Intermediate core strengthening workout focusing on stability and power.',
      duration: 6,
      difficulty: 'Intermediate',
      category: 'Abs & Core',
      equipment: 'No Equipment',
      imageUrl: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400',
      instructorName: 'Mike Chen',
      tags: ['Strength', 'Core', 'Stability'],
      rating: 4.6,
      totalRatings: 892,
      caloriesBurn: 55,
      muscleGroups: ['Core', 'Lower Back'],
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
    FitnessClass(
      id: 'abs-3',
      title: '5-Min Runner\'s Bodyweight Burn: Plank Party',
      description: 'Dynamic plank variations designed specifically for runners to build core endurance.',
      duration: 7,
      difficulty: 'Beginner',
      category: 'Abs & Core',
      equipment: 'No Equipment',
      imageUrl: 'https://images.unsplash.com/photo-1518611012118-696072aa579a?w=400',
      instructorName: 'Emma Rodriguez',
      tags: ['Endurance', 'Plank', 'Running'],
      rating: 4.7,
      totalRatings: 1156,
      caloriesBurn: 50,
      muscleGroups: ['Core', 'Shoulders'],
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
    FitnessClass(
      id: 'abs-4',
      title: '5-Min Runner\'s Bodyweight Burn: Standing Core',
      description: 'Standing core exercises perfect for runners who want to strengthen without getting on the floor.',
      duration: 7,
      difficulty: 'Beginner',
      category: 'Abs & Core',
      equipment: 'No Equipment',
      imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
      instructorName: 'David Kim',
      tags: ['Endurance', 'Standing', 'Running'],
      rating: 4.5,
      totalRatings: 743,
      caloriesBurn: 48,
      muscleGroups: ['Core', 'Obliques'],
      createdAt: DateTime.now().subtract(const Duration(days: 4)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
    FitnessClass(
      id: 'abs-5',
      title: 'Core Strength (Adaptive)',
      description: 'Adaptive core workout that can be modified for different fitness levels and physical limitations.',
      duration: 8,
      difficulty: 'Beginner',
      category: 'Abs & Core',
      equipment: 'No Equipment',
      imageUrl: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400',
      instructorName: 'Lisa Thompson',
      tags: ['Adaptive', 'Inclusive', 'Gentle'],
      rating: 4.9,
      totalRatings: 567,
      caloriesBurn: 40,
      muscleGroups: ['Core'],
      createdAt: DateTime.now().subtract(const Duration(days: 6)),
      updatedAt: DateTime.now().subtract(const Duration(days: 4)),
    ),

    // Arms & Shoulders classes
    FitnessClass(
      id: 'arms-1',
      title: 'Upper Body Blast',
      description: 'Intense upper body workout targeting arms, shoulders, and chest.',
      duration: 15,
      difficulty: 'Intermediate',
      category: 'Arms & Shoulders',
      equipment: 'Dumbbells',
      imageUrl: 'https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=400',
      instructorName: 'Alex Martinez',
      tags: ['Strength', 'Upper Body', 'Muscle Building'],
      rating: 4.7,
      totalRatings: 934,
      caloriesBurn: 120,
      muscleGroups: ['Arms', 'Shoulders', 'Chest'],
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      updatedAt: DateTime.now(),
    ),
    FitnessClass(
      id: 'arms-2',
      title: 'Shoulder Sculpt',
      description: 'Targeted shoulder workout for definition and strength.',
      duration: 12,
      difficulty: 'Beginner',
      category: 'Arms & Shoulders',
      equipment: 'No Equipment',
      imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
      instructorName: 'Rachel Green',
      tags: ['Sculpting', 'Definition', 'Bodyweight'],
      rating: 4.4,
      totalRatings: 678,
      caloriesBurn: 85,
      muscleGroups: ['Shoulders', 'Arms'],
      createdAt: DateTime.now().subtract(const Duration(days: 7)),
      updatedAt: DateTime.now().subtract(const Duration(days: 5)),
    ),

    // Glutes & Legs classes
    FitnessClass(
      id: 'legs-1',
      title: 'Glute Activation',
      description: 'Wake up your glutes with this targeted activation routine.',
      duration: 10,
      difficulty: 'Beginner',
      category: 'Glutes & Legs',
      equipment: 'Resistance Bands',
      imageUrl: 'https://images.unsplash.com/photo-1518611012118-696072aa579a?w=400',
      instructorName: 'Jessica Brown',
      tags: ['Glutes', 'Activation', 'Warm-up'],
      rating: 4.8,
      totalRatings: 1523,
      caloriesBurn: 70,
      muscleGroups: ['Glutes', 'Hips'],
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    FitnessClass(
      id: 'legs-2',
      title: 'Lower Body Power',
      description: 'High-intensity lower body workout for strength and power.',
      duration: 20,
      difficulty: 'Advanced',
      category: 'Glutes & Legs',
      equipment: 'No Equipment',
      imageUrl: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400',
      instructorName: 'Marcus Johnson',
      tags: ['Power', 'HIIT', 'Strength'],
      rating: 4.6,
      totalRatings: 789,
      caloriesBurn: 180,
      muscleGroups: ['Glutes', 'Quads', 'Hamstrings'],
      createdAt: DateTime.now().subtract(const Duration(days: 8)),
      updatedAt: DateTime.now().subtract(const Duration(days: 6)),
    ),

    // Full Body classes
    FitnessClass(
      id: 'fullbody-1',
      title: 'Total Body Burn',
      description: 'Complete full-body workout that targets every muscle group.',
      duration: 25,
      difficulty: 'Intermediate',
      category: 'Full Body',
      equipment: 'No Equipment',
      imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
      instructorName: 'Taylor Swift',
      tags: ['Full Body', 'Burn', 'Complete'],
      rating: 4.9,
      totalRatings: 2156,
      caloriesBurn: 220,
      muscleGroups: ['Full Body'],
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      updatedAt: DateTime.now(),
    ),

    // Cardio classes
    FitnessClass(
      id: 'cardio-1',
      title: 'HIIT Cardio Blast',
      description: 'High-intensity interval training for maximum calorie burn.',
      duration: 18,
      difficulty: 'Advanced',
      category: 'Cardio',
      equipment: 'No Equipment',
      imageUrl: 'https://images.unsplash.com/photo-1518611012118-696072aa579a?w=400',
      instructorName: 'Chris Evans',
      tags: ['HIIT', 'Cardio', 'Fat Burn'],
      rating: 4.7,
      totalRatings: 1834,
      caloriesBurn: 250,
      muscleGroups: ['Cardiovascular'],
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      updatedAt: DateTime.now(),
    ),

    // Yoga classes
    FitnessClass(
      id: 'yoga-1',
      title: 'Morning Flow',
      description: 'Gentle yoga flow to start your day with mindfulness and flexibility.',
      duration: 30,
      difficulty: 'Beginner',
      category: 'Yoga',
      equipment: 'Yoga Mat',
      imageUrl: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400',
      instructorName: 'Zen Master',
      tags: ['Flow', 'Morning', 'Flexibility'],
      rating: 4.8,
      totalRatings: 967,
      caloriesBurn: 120,
      muscleGroups: ['Full Body', 'Flexibility'],
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
  ];

  /// Get all categories
  Future<List<ClassCategory>> getCategories() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    return List.from(_mockCategories);
  }

  /// Get classes by category
  Future<List<FitnessClass>> getClassesByCategory({
    required String categoryId,
    ClassFilter? filter,
    int? limit,
    int? offset,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));
    
    // Find category name from ID
    final category = _mockCategories.firstWhere(
      (cat) => cat.id == categoryId,
      orElse: () => _mockCategories.first,
    );
    
    var classes = _mockClasses.where((cls) => cls.category == category.name).toList();
    
    // Apply filters if provided
    if (filter != null) {
      classes = _applyFilters(classes, filter);
    }
    
    // Apply pagination
    if (offset != null) {
      classes = classes.skip(offset).toList();
    }
    if (limit != null) {
      classes = classes.take(limit).toList();
    }
    
    return classes;
  }

  /// Get all classes
  Future<List<FitnessClass>> getAllClasses({
    ClassFilter? filter,
    int? limit,
    int? offset,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 600));
    
    var classes = List<FitnessClass>.from(_mockClasses);
    
    // Apply filters if provided
    if (filter != null) {
      classes = _applyFilters(classes, filter);
    }
    
    // Apply pagination
    if (offset != null) {
      classes = classes.skip(offset).toList();
    }
    if (limit != null) {
      classes = classes.take(limit).toList();
    }
    
    return classes;
  }

  /// Apply filters to classes list
  List<FitnessClass> _applyFilters(List<FitnessClass> classes, ClassFilter filter) {
    var filteredClasses = classes;
    
    // Filter by categories
    if (filter.categories.isNotEmpty) {
      filteredClasses = filteredClasses.where((cls) => 
        filter.categories.contains(cls.category)).toList();
    }
    
    // Filter by difficulties
    if (filter.difficulties.isNotEmpty) {
      filteredClasses = filteredClasses.where((cls) => 
        filter.difficulties.contains(cls.difficulty)).toList();
    }
    
    // Filter by equipment
    if (filter.equipment.isNotEmpty) {
      filteredClasses = filteredClasses.where((cls) => 
        filter.equipment.contains(cls.equipment)).toList();
    }
    
    // Filter by no equipment requirement
    if (filter.requiresNoEquipment) {
      filteredClasses = filteredClasses.where((cls) => 
        cls.equipment == 'No Equipment').toList();
    }
    
    // Filter by search query
    if (filter.searchQuery?.isNotEmpty == true) {
      final query = filter.searchQuery!.toLowerCase();
      filteredClasses = filteredClasses.where((cls) => 
        cls.title.toLowerCase().contains(query) ||
        cls.description.toLowerCase().contains(query) ||
        cls.instructorName.toLowerCase().contains(query) ||
        cls.tags.any((tag) => tag.toLowerCase().contains(query))).toList();
    }
    
    // Filter by rating
    if (filter.minRating != null) {
      filteredClasses = filteredClasses.where((cls) => 
        cls.rating != null && cls.rating! >= filter.minRating!).toList();
    }
    
    // Filter by duration
    if (filter.minDuration != null) {
      filteredClasses = filteredClasses.where((cls) => 
        cls.duration >= filter.minDuration!).toList();
    }
    if (filter.maxDuration != null) {
      filteredClasses = filteredClasses.where((cls) => 
        cls.duration <= filter.maxDuration!).toList();
    }
    
    // Apply sorting
    switch (filter.sortBy) {
      case ClassSortBy.newest:
        filteredClasses.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case ClassSortBy.oldest:
        filteredClasses.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case ClassSortBy.durationAsc:
        filteredClasses.sort((a, b) => a.duration.compareTo(b.duration));
        break;
      case ClassSortBy.durationDesc:
        filteredClasses.sort((a, b) => b.duration.compareTo(a.duration));
        break;
      case ClassSortBy.rating:
        filteredClasses.sort((a, b) => (b.rating ?? 0).compareTo(a.rating ?? 0));
        break;
      case ClassSortBy.popularity:
        filteredClasses.sort((a, b) => (b.totalRatings ?? 0).compareTo(a.totalRatings ?? 0));
        break;
      case ClassSortBy.alphabetical:
        filteredClasses.sort((a, b) => a.title.compareTo(b.title));
        break;
    }
    
    // Apply sort order
    if (filter.sortOrder == SortOrder.ascending) {
      filteredClasses = filteredClasses.reversed.toList();
    }
    
    return filteredClasses;
  }

  /// Search classes
  Future<List<FitnessClass>> searchClasses({
    required String query,
    ClassFilter? filter,
    int? limit,
    int? offset,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 700));
    
    final searchFilter = (filter ?? const ClassFilter()).copyWith(searchQuery: query);
    return getAllClasses(filter: searchFilter, limit: limit, offset: offset);
  }

  /// Get class by ID
  Future<FitnessClass?> getClassById(String classId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 300));
    
    try {
      return _mockClasses.firstWhere((cls) => cls.id == classId);
    } catch (e) {
      return null;
    }
  }

  /// Get featured classes
  Future<List<FitnessClass>> getFeaturedClasses({int? limit}) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    var featured = _mockClasses.where((cls) => cls.rating != null && cls.rating! >= 4.7).toList();
    featured.sort((a, b) => (b.rating ?? 0).compareTo(a.rating ?? 0));
    
    if (limit != null) {
      featured = featured.take(limit).toList();
    }
    
    return featured;
  }

  /// Toggle bookmark
  Future<void> toggleBookmark(String classId, bool isBookmarked) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 200));
    
    final index = _mockClasses.indexWhere((cls) => cls.id == classId);
    if (index != -1) {
      _mockClasses[index] = _mockClasses[index].copyWith(isBookmarked: isBookmarked);
    }
  }
}
