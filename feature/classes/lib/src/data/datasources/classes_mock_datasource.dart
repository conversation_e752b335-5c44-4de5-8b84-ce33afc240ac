import '../../domain/models/fitness_class.dart';
import '../../domain/models/class_category.dart';
import '../../domain/models/class_filter.dart';

/// Mock data source for classes data - provides realistic test data
/// 
/// This class simulates API responses with realistic delays and data.
/// Perfect for development and testing without needing a real backend.
class ClassesMockDataSource {
  ClassesMockDataSource();

  // Mock categories data
  static final List<ClassCategory> _mockCategories = [
    const ClassCategory(
      id: 'abs-core',
      name: 'Abs & Core',
      description: 'Strengthen your core with targeted exercises',
      icon: '💪',
      color: '#FF6B6B',
      isActive: true,
      classCount: 5,
    ),
    const ClassCategory(
      id: 'arms-shoulders',
      name: 'Arms & Shoulders',
      description: 'Build upper body strength and definition',
      icon: '💪',
      color: '#4ECDC4',
      isActive: true,
      classCount: 3,
    ),
    const ClassCategory(
      id: 'glutes-legs',
      name: 'Glutes & Legs',
      description: 'Power up your lower body',
      icon: '🦵',
      color: '#45B7D1',
      isActive: true,
      classCount: 4,
    ),
    const ClassCategory(
      id: 'full-body',
      name: 'Full Body',
      description: 'Complete workout targeting all muscle groups',
      icon: '🏃',
      color: '#96CEB4',
      isActive: true,
      classCount: 3,
    ),
    const ClassCategory(
      id: 'cardio',
      name: 'Cardio',
      description: 'Get your heart pumping with cardio workouts',
      icon: '❤️',
      color: '#FFEAA7',
      isActive: true,
      classCount: 2,
    ),
    const ClassCategory(
      id: 'yoga',
      name: 'Yoga',
      description: 'Find balance and flexibility',
      icon: '🧘',
      color: '#DDA0DD',
      isActive: true,
      classCount: 2,
    ),
  ];

  // Mock classes data - simplified for new model
  static final List<FitnessClass> _mockClasses = [
    // Abs & Core classes
    FitnessClass(
      id: 'abs-1',
      name: '5-Min Bodyweight Burn: Core Wake-Up Call',
      description: 'Quick and effective core workout to start your day with energy and strength.',
      category: 'Abs & Core',
      startDate: DateTime.now().add(const Duration(hours: 1)),
      endDate: DateTime.now().add(const Duration(hours: 1, minutes: 6)),
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    FitnessClass(
      id: 'abs-2',
      name: 'Core Strength',
      description: 'Intermediate core strengthening workout focusing on stability and power.',
      category: 'Abs & Core',
      startDate: DateTime.now().add(const Duration(hours: 2)),
      endDate: DateTime.now().add(const Duration(hours: 2, minutes: 6)),
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
    FitnessClass(
      id: 'abs-3',
      name: 'Plank Party',
      description: 'Dynamic plank variations designed to build core endurance.',
      category: 'Abs & Core',
      startDate: DateTime.now().add(const Duration(hours: 3)),
      endDate: DateTime.now().add(const Duration(hours: 3, minutes: 7)),
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
    
    // Arms & Shoulders classes
    FitnessClass(
      id: 'arms-1',
      name: 'Upper Body Blast',
      description: 'Strengthen your arms and shoulders with this targeted workout.',
      category: 'Arms & Shoulders',
      startDate: DateTime.now().add(const Duration(hours: 4)),
      endDate: DateTime.now().add(const Duration(hours: 4, minutes: 15)),
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      updatedAt: DateTime.now(),
    ),
    FitnessClass(
      id: 'arms-2',
      name: 'Shoulder Sculpt',
      description: 'Targeted shoulder workout for definition and strength.',
      category: 'Arms & Shoulders',
      startDate: DateTime.now().add(const Duration(hours: 5)),
      endDate: DateTime.now().add(const Duration(hours: 5, minutes: 12)),
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 7)),
      updatedAt: DateTime.now().subtract(const Duration(days: 5)),
    ),
    
    // Glutes & Legs classes
    FitnessClass(
      id: 'legs-1',
      name: 'Lower Body Power',
      description: 'Build strength and power in your glutes and legs.',
      category: 'Glutes & Legs',
      startDate: DateTime.now().add(const Duration(hours: 6)),
      endDate: DateTime.now().add(const Duration(hours: 6, minutes: 20)),
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    FitnessClass(
      id: 'legs-2',
      name: 'Glute Activation',
      description: 'Wake up your glutes with this targeted activation routine.',
      category: 'Glutes & Legs',
      startDate: DateTime.now().add(const Duration(hours: 7)),
      endDate: DateTime.now().add(const Duration(hours: 7, minutes: 10)),
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
    ),
    
    // Full Body classes
    FitnessClass(
      id: 'full-1',
      name: 'Total Body Burn',
      description: 'Complete full-body workout targeting all major muscle groups.',
      category: 'Full Body',
      startDate: DateTime.now().add(const Duration(hours: 8)),
      endDate: DateTime.now().add(const Duration(hours: 8, minutes: 30)),
      isActive: false, // Inactive class for testing
      createdAt: DateTime.now().subtract(const Duration(days: 7)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
    ),
    FitnessClass(
      id: 'full-2',
      name: 'HIIT Full Body',
      description: 'High-intensity interval training for complete body conditioning.',
      category: 'Full Body',
      startDate: DateTime.now().add(const Duration(hours: 9)),
      endDate: DateTime.now().add(const Duration(hours: 9, minutes: 25)),
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      updatedAt: DateTime.now(),
    ),
    
    // Cardio classes
    FitnessClass(
      id: 'cardio-1',
      name: 'HIIT Cardio Blast',
      description: 'High-intensity interval training for maximum calorie burn.',
      category: 'Cardio',
      startDate: DateTime.now().add(const Duration(hours: 10)),
      endDate: DateTime.now().add(const Duration(hours: 10, minutes: 18)),
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      updatedAt: DateTime.now(),
    ),
    
    // Yoga classes
    FitnessClass(
      id: 'yoga-1',
      name: 'Morning Flow',
      description: 'Gentle yoga flow to start your day with mindfulness and flexibility.',
      category: 'Yoga',
      startDate: DateTime.now().add(const Duration(hours: 11)),
      endDate: DateTime.now().add(const Duration(hours: 11, minutes: 30)),
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
    ),
  ];

  /// Get all categories
  Future<List<ClassCategory>> getCategories() async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));
    return List.from(_mockCategories);
  }

  /// Get classes by category
  Future<List<FitnessClass>> getClassesByCategory({
    required String categoryId,
    ClassFilter? filter,
    int? limit,
    int? offset,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));
    
    // Find category name from ID
    final category = _mockCategories.firstWhere(
      (cat) => cat.id == categoryId,
      orElse: () => _mockCategories.first,
    );
    
    var classes = _mockClasses.where((cls) => cls.category == category.name).toList();
    
    // Apply filters if provided
    if (filter != null) {
      classes = _applyFilters(classes, filter);
    }
    
    // Apply pagination
    if (offset != null) {
      classes = classes.skip(offset).toList();
    }
    if (limit != null) {
      classes = classes.take(limit).toList();
    }
    
    return classes;
  }

  /// Get all classes
  Future<List<FitnessClass>> getAllClasses({
    ClassFilter? filter,
    int? limit,
    int? offset,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 600));
    
    var classes = List<FitnessClass>.from(_mockClasses);
    
    // Apply filters if provided
    if (filter != null) {
      classes = _applyFilters(classes, filter);
    }
    
    // Apply pagination
    if (offset != null) {
      classes = classes.skip(offset).toList();
    }
    if (limit != null) {
      classes = classes.take(limit).toList();
    }
    
    return classes;
  }

  /// Search classes
  Future<List<FitnessClass>> searchClasses({
    required String query,
    ClassFilter? filter,
    int? limit,
    int? offset,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 700));
    
    final searchFilter = (filter ?? const ClassFilter()).copyWith(searchQuery: query);
    return getAllClasses(filter: searchFilter, limit: limit, offset: offset);
  }

  /// Get class by ID
  Future<FitnessClass?> getClassById(String classId) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 300));
    
    try {
      return _mockClasses.firstWhere((cls) => cls.id == classId);
    } catch (e) {
      return null;
    }
  }

  /// Get featured classes
  Future<List<FitnessClass>> getFeaturedClasses({int? limit}) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 500));

    var featured = _mockClasses.where((cls) => cls.isActive).toList();
    featured.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    if (limit != null) {
      featured = featured.take(limit).toList();
    }

    return featured;
  }

  /// Apply filters to classes list
  List<FitnessClass> _applyFilters(List<FitnessClass> classes, ClassFilter filter) {
    var filteredClasses = classes;

    // Filter by categories
    if (filter.categories.isNotEmpty) {
      filteredClasses = filteredClasses.where((cls) =>
        filter.categories.contains(cls.category)).toList();
    }

    // Filter by search query
    if (filter.searchQuery?.isNotEmpty == true) {
      final query = filter.searchQuery!.toLowerCase();
      filteredClasses = filteredClasses.where((cls) =>
        cls.name.toLowerCase().contains(query) ||
        cls.description.toLowerCase().contains(query) ||
        cls.category.toLowerCase().contains(query)).toList();
    }

    // Apply sorting
    switch (filter.sortBy) {
      case ClassSortBy.newest:
        filteredClasses.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case ClassSortBy.oldest:
        filteredClasses.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case ClassSortBy.durationAsc:
        filteredClasses.sort((a, b) => a.durationInMinutes.compareTo(b.durationInMinutes));
        break;
      case ClassSortBy.durationDesc:
        filteredClasses.sort((a, b) => b.durationInMinutes.compareTo(a.durationInMinutes));
        break;
      case ClassSortBy.alphabetical:
        filteredClasses.sort((a, b) => a.name.compareTo(b.name));
        break;
      default:
        // Default sorting by newest
        filteredClasses.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
    }

    return filteredClasses;
  }

  /// Toggle bookmark status for a class
  Future<void> toggleBookmark(String classId, bool isBookmarked) async {
    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 200));

    // In a real implementation, this would update the server
    // For mock, we just simulate success
    print('📌 Toggled bookmark for class $classId: $isBookmarked');
  }
}
