import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../domain/models/fitness_class.dart';
import '../../domain/models/class_category.dart';
import '../../domain/models/class_filter.dart';

/// API data source for classes data - ready for real API integration
///
/// This class handles all HTTP requests related to fitness classes.
/// It uses the http package for network operations.
/// Updated to match API requirement specification.
class ClassesApiDataSource {
  ClassesApiDataSource({
    required String baseUrl,
    http.Client? httpClient,
    String? apiKey,
  }) : _baseUrl = baseUrl,
       _httpClient = httpClient ?? http.Client(),
       _apiKey = apiKey ?? 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b';

  final String _baseUrl;
  final http.Client _httpClient;
  final String _apiKey;

  /// Get default headers with API key
  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'x-api-key': _apiKey,
  };

  /// Get all available class categories
  Future<List<ClassCategory>> getCategories() async {
    try {
      print('🌐 API Call: GET $_baseUrl/api/public/v1/classes/categories');
      print('🔑 Headers: $_headers');

      final response = await _httpClient.get(
        Uri.parse('$_baseUrl/api/public/v1/classes/categories'),
        headers: _headers,
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw ClassesApiException('Request timeout - API server might be down');
        },
      );

      print('📡 Response Status: ${response.statusCode}');
      print('📄 Response Body Length: ${response.body.length}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        List<ClassCategory> categories = [];

        // Handle API response structure
        if (data is Map<String, dynamic> &&
            data['success'] == true &&
            data['data'] is Map) {
          final dataMap = data['data'] as Map<String, dynamic>;
          if (dataMap['categories'] is List) {
            categories = (dataMap['categories'] as List)
                .map((json) => ClassCategory.fromJson(json as Map<String, dynamic>))
                .toList();
          }
        } else if (data is List) {
          categories = data
              .map((json) => ClassCategory.fromJson(json as Map<String, dynamic>))
              .toList();
        }

        print('✅ Parsed ${categories.length} categories from API');
        return categories;
      } else if (response.statusCode == 401) {
        throw ClassesApiException('Unauthorized - Invalid API key');
      } else if (response.statusCode == 404) {
        throw ClassesApiException('Categories endpoint not found - Check server configuration');
      } else if (response.statusCode >= 500) {
        throw ClassesApiException('Server error (${response.statusCode}) - API server might be down');
      } else {
        throw ClassesApiException('HTTP ${response.statusCode}: ${response.body}');
      }
    } on ClassesApiException {
      rethrow;
    } catch (e) {
      print('❌ API Error fetching categories: $e');
      if (e.toString().contains('SocketException') || e.toString().contains('Connection refused')) {
        throw ClassesApiException('Cannot connect to API server at $_baseUrl - Make sure server is running');
      }
      throw ClassesApiException('Failed to fetch categories: $e');
    }
  }

  /// Get classes by category with optional filtering
  Future<List<FitnessClass>> getClassesByCategory({
    required String categoryId,
    ClassFilter? filter,
    int? limit,
    int? offset,
  }) async {
    try {
      final queryParams = <String, String>{
        'categoryId': categoryId,
      };
      
      // Add filter parameters
      if (filter != null) {
        _addFilterParams(queryParams, filter);
      }
      
      // Add pagination parameters
      if (limit != null) queryParams['limit'] = limit.toString();
      if (offset != null) queryParams['offset'] = offset.toString();
      
      final uri = Uri.parse('$_baseUrl/api/public/v1/classes/category').replace(
        queryParameters: queryParams,
      );

      print('🌐 API Call: GET $uri');
      print('🔑 Headers: $_headers');

      final response = await _httpClient.get(
        uri,
        headers: _headers,
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return _parseClassesResponse(data);
      } else {
        throw ClassesApiException('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      print('❌ API Error fetching classes by category: $e');
      throw ClassesApiException('Failed to fetch classes by category: $e');
    }
  }

  /// Get all classes with optional filtering
  Future<List<FitnessClass>> getAllClasses({
    ClassFilter? filter,
    int? limit,
    int? offset,
  }) async {
    try {
      final queryParams = <String, String>{};

      // Add filter parameters
      if (filter != null) {
        _addFilterParams(queryParams, filter);
      }

      // Add pagination parameters
      if (limit != null) queryParams['limit'] = limit.toString();
      if (offset != null) queryParams['offset'] = offset.toString();

      final uri = Uri.parse('$_baseUrl/api/public/v1/classes').replace(
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      print('🌐 API Call: GET $uri');
      print('🔑 Headers: $_headers');
      print('📊 Query Params: $queryParams');

      final response = await _httpClient.get(
        uri,
        headers: _headers,
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw ClassesApiException('Request timeout - API server might be down');
        },
      );

      print('📡 Response Status: ${response.statusCode}');
      print('📄 Response Body Length: ${response.body.length}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final classes = _parseClassesResponse(data);
        print('✅ Successfully parsed ${classes.length} classes');
        return classes;
      } else if (response.statusCode == 401) {
        throw ClassesApiException('Unauthorized - Invalid API key');
      } else if (response.statusCode == 404) {
        throw ClassesApiException('API endpoint not found - Check server configuration');
      } else if (response.statusCode >= 500) {
        throw ClassesApiException('Server error (${response.statusCode}) - API server might be down');
      } else {
        throw ClassesApiException('HTTP ${response.statusCode}: ${response.body}');
      }
    } on ClassesApiException {
      rethrow;
    } catch (e) {
      print('❌ API Error fetching all classes: $e');
      if (e.toString().contains('SocketException') || e.toString().contains('Connection refused')) {
        throw ClassesApiException('Cannot connect to API server at $_baseUrl - Make sure server is running');
      }
      throw ClassesApiException('Failed to fetch all classes: $e');
    }
  }

  /// Search classes by query
  Future<List<FitnessClass>> searchClasses({
    required String query,
    ClassFilter? filter,
    int? limit,
    int? offset,
  }) async {
    try {
      final queryParams = <String, String>{
        'q': query,
      };
      
      // Add filter parameters
      if (filter != null) {
        _addFilterParams(queryParams, filter);
      }
      
      // Add pagination parameters
      if (limit != null) queryParams['limit'] = limit.toString();
      if (offset != null) queryParams['offset'] = offset.toString();
      
      final uri = Uri.parse('$_baseUrl/api/public/v1/classes/search').replace(
        queryParameters: queryParams,
      );

      print('🔍 API Call: GET $uri');
      print('🔑 Headers: $_headers');

      final response = await _httpClient.get(
        uri,
        headers: _headers,
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return _parseClassesResponse(data);
      } else {
        throw ClassesApiException('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      print('❌ API Error searching classes: $e');
      throw ClassesApiException('Failed to search classes: $e');
    }
  }

  /// Get class by ID
  Future<FitnessClass?> getClassById(String classId) async {
    try {
      print('🔍 API Call: GET $_baseUrl/api/public/v1/classes/$classId');
      print('🔑 Headers: $_headers');

      final response = await _httpClient.get(
        Uri.parse('$_baseUrl/api/public/v1/classes/$classId'),
        headers: _headers,
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data is Map<String, dynamic> && 
            data['success'] == true && 
            data['data'] is Map) {
          final fitnessClass = FitnessClass.fromJson(data['data'] as Map<String, dynamic>);
          print('✅ Found class: ${fitnessClass.title}');
          return fitnessClass;
        } else if (data is Map<String, dynamic>) {
          final fitnessClass = FitnessClass.fromJson(data);
          print('✅ Found class: ${fitnessClass.title}');
          return fitnessClass;
        }
      } else if (response.statusCode == 404) {
        print('⚠️ Class not found');
        return null;
      } else {
        throw ClassesApiException('HTTP ${response.statusCode}: ${response.body}');
      }
      
      return null;
    } catch (e) {
      print('❌ API Error fetching class by ID: $e');
      throw ClassesApiException('Failed to fetch class by ID: $e');
    }
  }

  /// Get featured classes
  Future<List<FitnessClass>> getFeaturedClasses({int? limit}) async {
    try {
      final queryParams = <String, String>{};
      if (limit != null) queryParams['limit'] = limit.toString();
      
      final uri = Uri.parse('$_baseUrl/api/classes/featured').replace(
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );
      
      print('⭐ API Call: GET $uri');
      
      final response = await _httpClient.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return _parseClassesResponse(data);
      } else {
        throw ClassesApiException('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      print('❌ API Error fetching featured classes: $e');
      throw ClassesApiException('Failed to fetch featured classes: $e');
    }
  }

  /// Toggle bookmark status
  Future<void> toggleBookmark(String classId, bool isBookmarked) async {
    try {
      final method = isBookmarked ? 'POST' : 'DELETE';
      print('📝 API Call: $method $_baseUrl/api/classes/$classId/bookmark');
      
      final response = isBookmarked
          ? await _httpClient.post(
              Uri.parse('$_baseUrl/api/classes/$classId/bookmark'),
              headers: {'Content-Type': 'application/json'},
            )
          : await _httpClient.delete(
              Uri.parse('$_baseUrl/api/classes/$classId/bookmark'),
              headers: {'Content-Type': 'application/json'},
            );
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        print('✅ Successfully ${isBookmarked ? 'bookmarked' : 'unbookmarked'} class');
      } else {
        throw ClassesApiException('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      print('❌ API Error toggling bookmark: $e');
      throw ClassesApiException('Failed to toggle bookmark: $e');
    }
  }

  /// Rate a class
  Future<void> rateClass(String classId, double rating) async {
    try {
      print('⭐ API Call: POST $_baseUrl/api/classes/$classId/rate');
      
      final response = await _httpClient.post(
        Uri.parse('$_baseUrl/api/classes/$classId/rate'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'rating': rating}),
      );
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        print('✅ Successfully rated class');
      } else {
        throw ClassesApiException('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      print('❌ API Error rating class: $e');
      throw ClassesApiException('Failed to rate class: $e');
    }
  }

  /// Helper method to add filter parameters to query
  void _addFilterParams(Map<String, String> queryParams, ClassFilter filter) {
    if (filter.categories.isNotEmpty) {
      queryParams['categories'] = filter.categories.join(',');
    }
    if (filter.difficulties.isNotEmpty) {
      queryParams['difficulties'] = filter.difficulties.join(',');
    }
    if (filter.equipment.isNotEmpty) {
      queryParams['equipment'] = filter.equipment.join(',');
    }
    if (filter.searchQuery?.isNotEmpty == true) {
      queryParams['search'] = filter.searchQuery!;
    }
    if (filter.minRating != null) {
      queryParams['minRating'] = filter.minRating.toString();
    }
    if (filter.minDuration != null) {
      queryParams['minDuration'] = filter.minDuration.toString();
    }
    if (filter.maxDuration != null) {
      queryParams['maxDuration'] = filter.maxDuration.toString();
    }
    if (filter.requiresNoEquipment) {
      queryParams['noEquipment'] = 'true';
    }
    queryParams['sortBy'] = filter.sortBy.name;
    queryParams['sortOrder'] = filter.sortOrder.name;
  }

  /// Helper method to parse classes response
  List<FitnessClass> _parseClassesResponse(dynamic data) {
    if (data != null) {
      List<FitnessClass> classes = [];
      
      // Handle API response structure
      if (data is Map<String, dynamic> && 
          data['success'] == true && 
          data['data'] is Map) {
        final dataMap = data['data'] as Map<String, dynamic>;
        if (dataMap['classes'] is List) {
          classes = (dataMap['classes'] as List)
              .map((json) => FitnessClass.fromJson(json as Map<String, dynamic>))
              .toList();
        }
      } else if (data is List) {
        classes = data
            .map((json) => FitnessClass.fromJson(json as Map<String, dynamic>))
            .toList();
      } else if (data is Map<String, dynamic> && data['data'] is List) {
        classes = (data['data'] as List)
            .map((json) => FitnessClass.fromJson(json as Map<String, dynamic>))
            .toList();
      }
      
      print('✅ Parsed ${classes.length} classes from API');
      return classes;
    }
    
    print('⚠️ No classes found in API response');
    return [];
  }
}

/// Custom exception for classes API operations
class ClassesApiException implements Exception {
  const ClassesApiException(this.message);
  
  final String message;
  
  @override
  String toString() => 'ClassesApiException: $message';
}
