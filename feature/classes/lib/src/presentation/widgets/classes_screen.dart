import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/classes_bloc.dart';
import '../bloc/classes_event.dart';
import '../bloc/classes_state.dart';
import 'category_tabs.dart';
import 'class_card.dart';
import 'class_detail_screen.dart';
import 'filter_bottom_sheet.dart';

/// Main classes screen matching the design from the image
class ClassesScreen extends StatefulWidget {
  const ClassesScreen({super.key});

  @override
  State<ClassesScreen> createState() => _ClassesScreenState();
}

class _ClassesScreenState extends State<ClassesScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    
    // Load initial data
    context.read<ClassesBloc>().add(const ClassesCategoriesRequested());
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<ClassesBloc>().add(const ClassesLoadMoreRequested());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const FilterBottomSheet(),
    );
  }

  void _showSortBottomSheet() {
    final currentState = context.read<ClassesBloc>().state;
    if (currentState is! ClassesLoaded) return;

    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sort By',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            // Add sort options here
            ListTile(
              title: const Text('Newest'),
              onTap: () {
                Navigator.pop(context);
                // Apply sort
              },
            ),
            ListTile(
              title: const Text('Duration (Short to Long)'),
              onTap: () {
                Navigator.pop(context);
                // Apply sort
              },
            ),
            ListTile(
              title: const Text('Highest Rated'),
              onTap: () {
                Navigator.pop(context);
                // Apply sort
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header with back button and title
            _buildHeader(),
            
            // Category tabs
            _buildCategoryTabs(),
            
            // Classes list
            Expanded(
              child: _buildClassesList(),
            ),
            
            // Bottom filter and sort bar
            _buildBottomBar(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return BlocBuilder<ClassesBloc, ClassesState>(
      builder: (context, state) {
        String title = 'Browse By';
        String subtitle = '';
        
        if (state is ClassesLoaded) {
          title = state.displayTitle;
          subtitle = state.displaySubtitle;
        }
        
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.arrow_back_ios, size: 20),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                    if (subtitle.isNotEmpty)
                      Text(
                        subtitle,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCategoryTabs() {
    return BlocBuilder<ClassesBloc, ClassesState>(
      builder: (context, state) {
        if (state is ClassesLoaded) {
          return CategoryTabs(
            categories: state.categories,
            selectedCategoryId: state.selectedCategoryId,
            onCategorySelected: (categoryId) {
              context.read<ClassesBloc>().add(
                ClassesCategoryChanged(categoryId: categoryId),
              );
            },
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildClassesList() {
    return BlocBuilder<ClassesBloc, ClassesState>(
      builder: (context, state) {
        if (state is ClassesLoading) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  color: Colors.black,
                ),
                SizedBox(height: 16),
                Text(
                  'Loading classes from API...',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 14,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Connecting to localhost:3000',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          );
        }
        
        if (state is ClassesError) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    state.message.contains('Cannot connect')
                        ? Icons.wifi_off
                        : state.message.contains('Server')
                            ? Icons.cloud_off
                            : Icons.error_outline,
                    size: 64,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    state.message.contains('Cannot connect')
                        ? 'Connection Error'
                        : state.message.contains('Server')
                            ? 'Server Error'
                            : 'Something went wrong',
                    style: Theme.of(context).textTheme.headlineSmall,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton.icon(
                        onPressed: () {
                          context.read<ClassesBloc>().add(const ClassesRefreshRequested());
                        },
                        icon: const Icon(Icons.refresh),
                        label: const Text('Try Again'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.black,
                          foregroundColor: Colors.white,
                        ),
                      ),
                      if (state.message.contains('Cannot connect')) ...[
                        const SizedBox(width: 12),
                        OutlinedButton.icon(
                          onPressed: () {
                            // Show API configuration help
                            _showApiConfigHelp(context);
                          },
                          icon: const Icon(Icons.help_outline),
                          label: const Text('Help'),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          );
        }
        
        if (state is ClassesLoaded) {
          if (state.classes.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.search_off,
                    size: 64,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No classes found',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Try adjusting your filters or search terms',
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            );
          }
          
          return RefreshIndicator(
            onRefresh: () async {
              context.read<ClassesBloc>().add(const ClassesRefreshRequested());
            },
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: state.classes.length + (state.hasMoreData ? 1 : 0),
              itemBuilder: (context, index) {
                if (index >= state.classes.length) {
                  // Loading indicator for pagination
                  return const Padding(
                    padding: EdgeInsets.all(16),
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                }
                
                final fitnessClass = state.classes[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: ClassCard(
                    fitnessClass: fitnessClass,
                    onTap: () {
                      // Navigate to class detail
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ClassDetailScreen(
                            fitnessClass: fitnessClass,
                          ),
                        ),
                      );
                    },
                    onBookmarkTap: () {
                      context.read<ClassesBloc>().add(
                        ClassesBookmarkToggled(
                          classId: fitnessClass.id,
                          isBookmarked: !fitnessClass.isBookmarked,
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          );
        }
        
        if (state is ClassesLoadingMore) {
          return RefreshIndicator(
            onRefresh: () async {
              context.read<ClassesBloc>().add(const ClassesRefreshRequested());
            },
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: state.currentClasses.length + 1,
              itemBuilder: (context, index) {
                if (index >= state.currentClasses.length) {
                  return const Padding(
                    padding: EdgeInsets.all(16),
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                }
                
                final fitnessClass = state.currentClasses[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: ClassCard(
                    fitnessClass: fitnessClass,
                    onTap: () {
                      // Navigate to class detail
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ClassDetailScreen(
                            fitnessClass: fitnessClass,
                          ),
                        ),
                      );
                    },
                    onBookmarkTap: () {
                      context.read<ClassesBloc>().add(
                        ClassesBookmarkToggled(
                          classId: fitnessClass.id,
                          isBookmarked: !fitnessClass.isBookmarked,
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          );
        }
        
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.vertical(top: Radius.circular(0)),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: _showFilterBottomSheet,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.tune,
                      color: Colors.white,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Filter',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Container(
            width: 1,
            height: 24,
            color: Colors.grey[600],
          ),
          Expanded(
            child: GestureDetector(
              onTap: _showSortBottomSheet,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.sort,
                      color: Colors.white,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Sort',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showApiConfigHelp(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('API Configuration Help'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'To use the Classes feature, make sure:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text('1. API server is running at:'),
              Text(
                'http://10.0.2.2:3000',
                style: TextStyle(fontFamily: 'monospace', backgroundColor: Color(0xFFF5F5F5)),
              ),
              SizedBox(height: 8),
              Text('2. API endpoint is available:'),
              Text(
                '/api/public/v1/classes',
                style: TextStyle(fontFamily: 'monospace', backgroundColor: Color(0xFFF5F5F5)),
              ),
              SizedBox(height: 8),
              Text('3. API key is configured:'),
              Text(
                'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b',
                style: TextStyle(fontFamily: 'monospace', backgroundColor: Color(0xFFF5F5F5), fontSize: 10),
              ),
              SizedBox(height: 12),
              Text(
                'If you\'re running in development, start your API server and try again.',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
