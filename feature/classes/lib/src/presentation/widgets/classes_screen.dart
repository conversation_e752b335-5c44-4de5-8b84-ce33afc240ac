import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/classes_bloc.dart';
import '../bloc/classes_event.dart';
import '../bloc/classes_state.dart';
import 'category_tabs.dart';
import 'class_card.dart';
import 'class_detail_screen.dart';
import 'filter_bottom_sheet.dart';

/// Main classes screen matching the design from the image
class ClassesScreen extends StatefulWidget {
  const ClassesScreen({super.key});

  @override
  State<ClassesScreen> createState() => _ClassesScreenState();
}

class _ClassesScreenState extends State<ClassesScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    
    // Load initial data
    context.read<ClassesBloc>().add(const ClassesCategoriesRequested());
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<ClassesBloc>().add(const ClassesLoadMoreRequested());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const FilterBottomSheet(),
    );
  }

  void _showSortBottomSheet() {
    final currentState = context.read<ClassesBloc>().state;
    if (currentState is! ClassesLoaded) return;

    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sort By',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            // Add sort options here
            ListTile(
              title: const Text('Newest'),
              onTap: () {
                Navigator.pop(context);
                // Apply sort
              },
            ),
            ListTile(
              title: const Text('Duration (Short to Long)'),
              onTap: () {
                Navigator.pop(context);
                // Apply sort
              },
            ),
            ListTile(
              title: const Text('Highest Rated'),
              onTap: () {
                Navigator.pop(context);
                // Apply sort
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            // Header with back button and title
            _buildHeader(),
            
            // Category tabs
            _buildCategoryTabs(),
            
            // Classes list
            Expanded(
              child: _buildClassesList(),
            ),
            
            // Bottom filter and sort bar
            _buildBottomBar(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return BlocBuilder<ClassesBloc, ClassesState>(
      builder: (context, state) {
        String title = 'Browse By';
        String subtitle = '';
        
        if (state is ClassesLoaded) {
          title = state.displayTitle;
          subtitle = state.displaySubtitle;
        }
        
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.arrow_back_ios, size: 20),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                    if (subtitle.isNotEmpty)
                      Text(
                        subtitle,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCategoryTabs() {
    return BlocBuilder<ClassesBloc, ClassesState>(
      builder: (context, state) {
        if (state is ClassesLoaded) {
          return CategoryTabs(
            categories: state.categories,
            selectedCategoryId: state.selectedCategoryId,
            onCategorySelected: (categoryId) {
              context.read<ClassesBloc>().add(
                ClassesCategoryChanged(categoryId: categoryId),
              );
            },
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildClassesList() {
    return BlocBuilder<ClassesBloc, ClassesState>(
      builder: (context, state) {
        if (state is ClassesLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
        
        if (state is ClassesError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.grey,
                ),
                const SizedBox(height: 16),
                Text(
                  'Something went wrong',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  state.message,
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.grey),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    context.read<ClassesBloc>().add(const ClassesRefreshRequested());
                  },
                  child: const Text('Try Again'),
                ),
              ],
            ),
          );
        }
        
        if (state is ClassesLoaded) {
          if (state.classes.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.search_off,
                    size: 64,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No classes found',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Try adjusting your filters or search terms',
                    textAlign: TextAlign.center,
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            );
          }
          
          return RefreshIndicator(
            onRefresh: () async {
              context.read<ClassesBloc>().add(const ClassesRefreshRequested());
            },
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: state.classes.length + (state.hasMoreData ? 1 : 0),
              itemBuilder: (context, index) {
                if (index >= state.classes.length) {
                  // Loading indicator for pagination
                  return const Padding(
                    padding: EdgeInsets.all(16),
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                }
                
                final fitnessClass = state.classes[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: ClassCard(
                    fitnessClass: fitnessClass,
                    onTap: () {
                      // Navigate to class detail
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ClassDetailScreen(
                            fitnessClass: fitnessClass,
                          ),
                        ),
                      );
                    },
                    onBookmarkTap: () {
                      context.read<ClassesBloc>().add(
                        ClassesBookmarkToggled(
                          classId: fitnessClass.id,
                          isBookmarked: !fitnessClass.isBookmarked,
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          );
        }
        
        if (state is ClassesLoadingMore) {
          return RefreshIndicator(
            onRefresh: () async {
              context.read<ClassesBloc>().add(const ClassesRefreshRequested());
            },
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: state.currentClasses.length + 1,
              itemBuilder: (context, index) {
                if (index >= state.currentClasses.length) {
                  return const Padding(
                    padding: EdgeInsets.all(16),
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                }
                
                final fitnessClass = state.currentClasses[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: ClassCard(
                    fitnessClass: fitnessClass,
                    onTap: () {
                      // Navigate to class detail
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => ClassDetailScreen(
                            fitnessClass: fitnessClass,
                          ),
                        ),
                      );
                    },
                    onBookmarkTap: () {
                      context.read<ClassesBloc>().add(
                        ClassesBookmarkToggled(
                          classId: fitnessClass.id,
                          isBookmarked: !fitnessClass.isBookmarked,
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          );
        }
        
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.vertical(top: Radius.circular(0)),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: _showFilterBottomSheet,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.tune,
                      color: Colors.white,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Filter',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Container(
            width: 1,
            height: 24,
            color: Colors.grey[600],
          ),
          Expanded(
            child: GestureDetector(
              onTap: _showSortBottomSheet,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.sort,
                      color: Colors.white,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Text(
                      'Sort',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
