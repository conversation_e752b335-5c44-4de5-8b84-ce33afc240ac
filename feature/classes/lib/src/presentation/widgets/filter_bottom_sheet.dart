import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/models/class_filter.dart';
import '../bloc/classes_bloc.dart';
import '../bloc/classes_event.dart';
import '../bloc/classes_state.dart';

/// Filter bottom sheet for classes
class FilterBottomSheet extends StatefulWidget {
  const FilterBottomSheet({super.key});

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  late ClassFilter _currentFilter;
  
  // Filter options
  final List<String> _difficulties = ['Beginner', 'Intermediate', 'Advanced'];
  final List<String> _equipmentOptions = [
    'No Equipment',
    'Dumbbells',
    'Resistance Bands',
    'Yoga Mat',
    'Kettlebells',
  ];
  final List<String> _durationRanges = [
    '5-10 min',
    '10-20 min',
    '20-30 min',
    '30+ min',
  ];

  @override
  void initState() {
    super.initState();
    final state = context.read<ClassesBloc>().state;
    if (state is ClassesLoaded) {
      _currentFilter = state.currentFilter;
    } else {
      _currentFilter = const ClassFilter();
    }
  }

  void _applyFilters() {
    context.read<ClassesBloc>().add(ClassesFilterApplied(filter: _currentFilter));
    Navigator.of(context).pop();
  }

  void _clearFilters() {
    setState(() {
      _currentFilter = const ClassFilter();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Filter',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  onPressed: _clearFilters,
                  child: const Text('Clear All'),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // Filter content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDifficultySection(),
                  const SizedBox(height: 24),
                  _buildEquipmentSection(),
                  const SizedBox(height: 24),
                  _buildDurationSection(),
                  const SizedBox(height: 24),
                  _buildRatingSection(),
                  const SizedBox(height: 24),
                  _buildNoEquipmentToggle(),
                ],
              ),
            ),
          ),
          
          // Apply button
          Container(
            padding: const EdgeInsets.all(20),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _applyFilters,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.black,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'Apply Filters${_currentFilter.hasActiveFilters ? ' (${_currentFilter.activeFilterCount})' : ''}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDifficultySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Difficulty',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _difficulties.map((difficulty) {
            final isSelected = _currentFilter.difficulties.contains(difficulty);
            return FilterChip(
              label: Text(difficulty),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _currentFilter = _currentFilter.copyWith(
                      difficulties: [..._currentFilter.difficulties, difficulty],
                    );
                  } else {
                    _currentFilter = _currentFilter.copyWith(
                      difficulties: _currentFilter.difficulties
                          .where((d) => d != difficulty)
                          .toList(),
                    );
                  }
                });
              },
              backgroundColor: Colors.grey[100],
              selectedColor: Colors.black,
              labelStyle: TextStyle(
                color: isSelected ? Colors.white : Colors.black,
              ),
              checkmarkColor: Colors.white,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildEquipmentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Equipment',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _equipmentOptions.map((equipment) {
            final isSelected = _currentFilter.equipment.contains(equipment);
            return FilterChip(
              label: Text(equipment),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _currentFilter = _currentFilter.copyWith(
                      equipment: [..._currentFilter.equipment, equipment],
                    );
                  } else {
                    _currentFilter = _currentFilter.copyWith(
                      equipment: _currentFilter.equipment
                          .where((e) => e != equipment)
                          .toList(),
                    );
                  }
                });
              },
              backgroundColor: Colors.grey[100],
              selectedColor: Colors.black,
              labelStyle: TextStyle(
                color: isSelected ? Colors.white : Colors.black,
              ),
              checkmarkColor: Colors.white,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDurationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Duration',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _durationRanges.map((duration) {
            final isSelected = _currentFilter.durations.contains(duration);
            return FilterChip(
              label: Text(duration),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _currentFilter = _currentFilter.copyWith(
                      durations: [..._currentFilter.durations, duration],
                    );
                  } else {
                    _currentFilter = _currentFilter.copyWith(
                      durations: _currentFilter.durations
                          .where((d) => d != duration)
                          .toList(),
                    );
                  }
                });
              },
              backgroundColor: Colors.grey[100],
              selectedColor: Colors.black,
              labelStyle: TextStyle(
                color: isSelected ? Colors.white : Colors.black,
              ),
              checkmarkColor: Colors.white,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildRatingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Minimum Rating',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: Slider(
                value: _currentFilter.minRating ?? 0.0,
                min: 0.0,
                max: 5.0,
                divisions: 10,
                activeColor: Colors.black,
                inactiveColor: Colors.grey[300],
                onChanged: (value) {
                  setState(() {
                    _currentFilter = _currentFilter.copyWith(
                      minRating: value == 0.0 ? null : value,
                    );
                  });
                },
              ),
            ),
            Text(
              _currentFilter.minRating?.toStringAsFixed(1) ?? '0.0',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNoEquipmentToggle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text(
          'No Equipment Only',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        Switch(
          value: _currentFilter.requiresNoEquipment,
          onChanged: (value) {
            setState(() {
              _currentFilter = _currentFilter.copyWith(
                requiresNoEquipment: value,
              );
            });
          },
          activeColor: Colors.black,
        ),
      ],
    );
  }
}
