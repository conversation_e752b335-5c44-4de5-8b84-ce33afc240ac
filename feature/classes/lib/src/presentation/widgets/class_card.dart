import 'package:flutter/material.dart';
import '../../domain/models/fitness_class.dart';

/// Class card widget matching the design from the image
class ClassCard extends StatelessWidget {
  const ClassCard({
    super.key,
    required this.fitnessClass,
    required this.onTap,
    required this.onBookmarkTap,
  });

  final FitnessClass fitnessClass;
  final VoidCallback onTap;
  final VoidCallback onBookmarkTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 120,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Class image
            _buildClassImage(),
            
            // Class details
            Expanded(
              child: _buildClassDetails(context),
            ),
            
            // Bookmark button
            _buildBookmarkButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildClassImage() {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          bottomLeft: Radius.circular(12),
        ),
        image: DecorationImage(
          image: NetworkImage(fitnessClass.imageUrl),
          fit: BoxFit.cover,
          onError: (exception, stackTrace) {
            // Handle image loading error
          },
        ),
      ),
      child: fitnessClass.imageUrl.isEmpty
          ? Container(
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                ),
              ),
              child: const Icon(
                Icons.fitness_center,
                size: 40,
                color: Colors.grey,
              ),
            )
          : null,
    );
  }

  Widget _buildClassDetails(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Title and tags
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                fitnessClass.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              _buildTags(),
            ],
          ),
          
          // Duration
          Text(
            fitnessClass.durationText,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTags() {
    // Create tags from difficulty, category, and equipment
    final tags = [
      fitnessClass.difficulty,
      if (fitnessClass.tags.isNotEmpty) fitnessClass.tags.first,
      fitnessClass.equipment,
    ];

    return Wrap(
      spacing: 4,
      children: tags.map((tag) {
        return Text(
          '$tag • ',
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        );
      }).toList()
        ..removeLast() // Remove the last bullet point
        ..add(
          Text(
            tags.last,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ),
    );
  }

  Widget _buildBookmarkButton() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GestureDetector(
        onTap: onBookmarkTap,
        child: Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            fitnessClass.isBookmarked 
                ? Icons.bookmark 
                : Icons.bookmark_border,
            size: 18,
            color: fitnessClass.isBookmarked 
                ? Colors.black 
                : Colors.grey[600],
          ),
        ),
      ),
    );
  }
}
