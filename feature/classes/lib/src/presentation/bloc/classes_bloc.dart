import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/repositories/classes_repository.dart';
import '../../domain/models/class_filter.dart';
import '../../domain/models/class_category.dart';import 'classes_event.dart';
import 'classes_state.dart';

/// BLoC for managing classes state and business logic
class ClassesBloc extends Bloc<ClassesEvent, ClassesState> {
  ClassesBloc({
    required ClassesRepository repository,
  }) : _repository = repository,
       super(const ClassesInitial()) {
    
    // Register event handlers
    on<ClassesCategoriesRequested>(_onCategoriesRequested);
    on<ClassesByCategoryRequested>(_onClassesByCategoryRequested);
    on<ClassesAllRequested>(_onClassesAllRequested);
    on<ClassesSearchRequested>(_onClassesSearchRequested);
    on<ClassesSearchCleared>(_onClassesSearchCleared);
    on<ClassesCategoryChanged>(_onClassesCategoryChanged);
    on<ClassesFilterApplied>(_onClassesFilterApplied);
    on<ClassesFilterCleared>(_onClassesFilterCleared);
    on<ClassesBookmarkToggled>(_onClassesBookmarkToggled);
    on<ClassesRatingSubmitted>(_onClassesRatingSubmitted);
    on<ClassesLoadMoreRequested>(_onClassesLoadMoreRequested);
    on<ClassesRefreshRequested>(_onClassesRefreshRequested);
    on<ClassesFeaturedRequested>(_onClassesFeaturedRequested);
    on<ClassesDetailRequested>(_onClassesDetailRequested);
  }

  final ClassesRepository _repository;
  
  // Pagination constants
  static const int _pageSize = 20;
  int _currentOffset = 0;

  /// Handle categories request
  Future<void> _onCategoriesRequested(
    ClassesCategoriesRequested event,
    Emitter<ClassesState> emit,
  ) async {
    try {
      emit(const ClassesLoading());
      
      final categories = await _repository.getCategories();
      
      // Load initial classes (all classes)
      final classes = await _repository.getAllClasses(
        limit: _pageSize,
        offset: 0,
      );
      
      _currentOffset = classes.length;
      
      emit(ClassesLoaded(
        classes: classes,
        categories: categories,
        selectedCategoryId: null,
        currentFilter: const ClassFilter(),
        searchQuery: null,
        hasMoreData: classes.length >= _pageSize,
        totalCount: classes.length,
      ));
    } catch (e) {
      print('❌ BLoC Error loading categories: $e');
      String errorMessage = 'Failed to load categories';

      if (e.toString().contains('Cannot connect to API server')) {
        errorMessage = 'Cannot connect to server. Please check your internet connection and make sure the API server is running.';
      } else if (e.toString().contains('Unauthorized')) {
        errorMessage = 'Authentication failed. Please check API configuration.';
      } else if (e.toString().contains('timeout')) {
        errorMessage = 'Request timed out. Please try again.';
      } else if (e.toString().contains('Server error')) {
        errorMessage = 'Server is currently unavailable. Please try again later.';
      }

      emit(ClassesError(message: errorMessage));
    }
  }

  /// Handle classes by category request
  Future<void> _onClassesByCategoryRequested(
    ClassesByCategoryRequested event,
    Emitter<ClassesState> emit,
  ) async {
    try {
      final currentState = state;
      
      if (!event.refresh && currentState is ClassesLoaded) {
        // Show loading more if not refreshing
        emit(ClassesLoadingMore(
          currentClasses: currentState.classes,
          categories: currentState.categories,
          selectedCategoryId: event.categoryId,
          currentFilter: event.filter ?? currentState.currentFilter,
          searchQuery: currentState.searchQuery,
        ));
      } else {
        emit(const ClassesLoading());
      }
      
      final offset = event.refresh ? 0 : _currentOffset;
      
      final classes = await _repository.getClassesByCategory(
        categoryId: event.categoryId,
        filter: event.filter,
        limit: _pageSize,
        offset: offset,
      );
      
      List<ClassCategory> categories = [];
      if (currentState is ClassesLoaded) {
        categories = currentState.categories;
      } else {
        categories = await _repository.getCategories();
      }
      
      final allClasses = event.refresh 
          ? classes 
          : (currentState is ClassesLoaded ? [...currentState.classes, ...classes] : classes);
      
      _currentOffset = allClasses.length;
      
      emit(ClassesLoaded(
        classes: allClasses,
        categories: categories,
        selectedCategoryId: event.categoryId,
        currentFilter: event.filter ?? const ClassFilter(),
        searchQuery: null,
        hasMoreData: classes.length >= _pageSize,
        totalCount: allClasses.length,
      ));
    } catch (e) {
      emit(ClassesError(
        message: 'Failed to load classes: $e',
        previousState: state,
      ));
    }
  }

  /// Handle all classes request
  Future<void> _onClassesAllRequested(
    ClassesAllRequested event,
    Emitter<ClassesState> emit,
  ) async {
    try {
      final currentState = state;
      
      if (!event.refresh && currentState is ClassesLoaded) {
        emit(ClassesLoadingMore(
          currentClasses: currentState.classes,
          categories: currentState.categories,
          selectedCategoryId: null,
          currentFilter: event.filter ?? currentState.currentFilter,
          searchQuery: currentState.searchQuery,
        ));
      } else {
        emit(const ClassesLoading());
      }
      
      final offset = event.refresh ? 0 : _currentOffset;
      
      final classes = await _repository.getAllClasses(
        filter: event.filter,
        limit: _pageSize,
        offset: offset,
      );
      
      List<ClassCategory> categories = [];
      if (currentState is ClassesLoaded) {
        categories = currentState.categories;
      } else {
        categories = await _repository.getCategories();
      }
      
      final allClasses = event.refresh 
          ? classes 
          : (currentState is ClassesLoaded ? [...currentState.classes, ...classes] : classes);
      
      _currentOffset = allClasses.length;
      
      emit(ClassesLoaded(
        classes: allClasses,
        categories: categories,
        selectedCategoryId: null,
        currentFilter: event.filter ?? const ClassFilter(),
        searchQuery: null,
        hasMoreData: classes.length >= _pageSize,
        totalCount: allClasses.length,
      ));
    } catch (e) {
      emit(ClassesError(
        message: 'Failed to load classes: $e',
        previousState: state,
      ));
    }
  }

  /// Handle search request
  Future<void> _onClassesSearchRequested(
    ClassesSearchRequested event,
    Emitter<ClassesState> emit,
  ) async {
    try {
      emit(const ClassesLoading());
      
      final classes = await _repository.searchClasses(
        query: event.query,
        filter: event.filter,
        limit: _pageSize,
        offset: 0,
      );
      
      final currentState = state;
      List<ClassCategory> categories = [];
      if (currentState is ClassesLoaded) {
        categories = currentState.categories;
      } else {
        categories = await _repository.getCategories();
      }
      
      _currentOffset = classes.length;
      
      emit(ClassesLoaded(
        classes: classes,
        categories: categories,
        selectedCategoryId: null,
        currentFilter: event.filter ?? const ClassFilter(),
        searchQuery: event.query,
        hasMoreData: classes.length >= _pageSize,
        totalCount: classes.length,
      ));
    } catch (e) {
      emit(ClassesError(message: 'Failed to search classes: $e'));
    }
  }

  /// Handle search cleared
  Future<void> _onClassesSearchCleared(
    ClassesSearchCleared event,
    Emitter<ClassesState> emit,
  ) async {
    final currentState = state;
    if (currentState is ClassesLoaded) {
      // Reload all classes
      add(const ClassesAllRequested(refresh: true));
    }
  }

  /// Handle category changed
  Future<void> _onClassesCategoryChanged(
    ClassesCategoryChanged event,
    Emitter<ClassesState> emit,
  ) async {
    final currentState = state;
    if (currentState is ClassesLoaded) {
      add(ClassesByCategoryRequested(
        categoryId: event.categoryId,
        filter: currentState.currentFilter,
        refresh: true,
      ));
    }
  }

  /// Handle filter applied
  Future<void> _onClassesFilterApplied(
    ClassesFilterApplied event,
    Emitter<ClassesState> emit,
  ) async {
    final currentState = state;
    if (currentState is ClassesLoaded) {
      if (currentState.selectedCategoryId != null) {
        add(ClassesByCategoryRequested(
          categoryId: currentState.selectedCategoryId!,
          filter: event.filter,
          refresh: true,
        ));
      } else if (currentState.searchQuery?.isNotEmpty == true) {
        add(ClassesSearchRequested(
          query: currentState.searchQuery!,
          filter: event.filter,
        ));
      } else {
        add(ClassesAllRequested(
          filter: event.filter,
          refresh: true,
        ));
      }
    }
  }

  /// Handle filter cleared
  Future<void> _onClassesFilterCleared(
    ClassesFilterCleared event,
    Emitter<ClassesState> emit,
  ) async {
    final currentState = state;
    if (currentState is ClassesLoaded) {
      add(ClassesFilterApplied(filter: const ClassFilter()));
    }
  }

  /// Handle bookmark toggle
  Future<void> _onClassesBookmarkToggled(
    ClassesBookmarkToggled event,
    Emitter<ClassesState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ClassesLoaded) return;
    
    try {
      emit(ClassesBookmarkLoading(
        classId: event.classId,
        currentState: currentState,
      ));
      
      await _repository.toggleBookmark(event.classId, event.isBookmarked);
      
      // Update the class in the current list
      final updatedClasses = currentState.classes.map((cls) {
        if (cls.id == event.classId) {
          return cls.copyWith(isBookmarked: event.isBookmarked);
        }
        return cls;
      }).toList();
      
      final updatedState = currentState.copyWith(classes: updatedClasses);
      
      emit(ClassesBookmarkSuccess(
        classId: event.classId,
        isBookmarked: event.isBookmarked,
        updatedState: updatedState,
      ));
      
      // Return to loaded state
      emit(updatedState);
    } catch (e) {
      emit(ClassesBookmarkError(
        message: 'Failed to ${event.isBookmarked ? 'bookmark' : 'unbookmark'} class: $e',
        classId: event.classId,
        previousState: currentState,
      ));
      
      // Return to previous state
      emit(currentState);
    }
  }

  /// Handle rating submission
  Future<void> _onClassesRatingSubmitted(
    ClassesRatingSubmitted event,
    Emitter<ClassesState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ClassesLoaded) return;
    
    try {
      emit(ClassesRatingLoading(
        classId: event.classId,
        currentState: currentState,
      ));
      
      await _repository.rateClass(event.classId, event.rating);
      
      emit(ClassesRatingSuccess(
        classId: event.classId,
        rating: event.rating,
        updatedState: currentState,
      ));
      
      // Return to loaded state
      emit(currentState);
    } catch (e) {
      emit(ClassesRatingError(
        message: 'Failed to rate class: $e',
        classId: event.classId,
        previousState: currentState,
      ));
      
      // Return to previous state
      emit(currentState);
    }
  }

  /// Handle load more request
  Future<void> _onClassesLoadMoreRequested(
    ClassesLoadMoreRequested event,
    Emitter<ClassesState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ClassesLoaded || !currentState.hasMoreData) return;
    
    if (currentState.selectedCategoryId != null) {
      add(ClassesByCategoryRequested(
        categoryId: currentState.selectedCategoryId!,
        filter: currentState.currentFilter,
        refresh: false,
      ));
    } else if (currentState.searchQuery?.isNotEmpty == true) {
      // For search, we don't support pagination yet
      return;
    } else {
      add(ClassesAllRequested(
        filter: currentState.currentFilter,
        refresh: false,
      ));
    }
  }

  /// Handle refresh request
  Future<void> _onClassesRefreshRequested(
    ClassesRefreshRequested event,
    Emitter<ClassesState> emit,
  ) async {
    final currentState = state;
    if (currentState is ClassesLoaded) {
      if (currentState.selectedCategoryId != null) {
        add(ClassesByCategoryRequested(
          categoryId: currentState.selectedCategoryId!,
          filter: currentState.currentFilter,
          refresh: true,
        ));
      } else if (currentState.searchQuery?.isNotEmpty == true) {
        add(ClassesSearchRequested(
          query: currentState.searchQuery!,
          filter: currentState.currentFilter,
        ));
      } else {
        add(ClassesAllRequested(
          filter: currentState.currentFilter,
          refresh: true,
        ));
      }
    } else {
      add(const ClassesCategoriesRequested());
    }
  }

  /// Handle featured classes request
  Future<void> _onClassesFeaturedRequested(
    ClassesFeaturedRequested event,
    Emitter<ClassesState> emit,
  ) async {
    try {
      final featuredClasses = await _repository.getFeaturedClasses(
        limit: event.limit,
      );
      
      final currentState = state;
      if (currentState is ClassesLoaded) {
        emit(currentState.copyWith(featuredClasses: featuredClasses));
      }
    } catch (e) {
      // Don't emit error for featured classes, just log it
      print('Failed to load featured classes: $e');
    }
  }

  /// Handle class detail request
  Future<void> _onClassesDetailRequested(
    ClassesDetailRequested event,
    Emitter<ClassesState> emit,
  ) async {
    try {
      final classDetail = await _repository.getClassById(event.classId);
      
      final currentState = state;
      if (currentState is ClassesLoaded && classDetail != null) {
        emit(currentState.copyWith(selectedClass: classDetail));
      }
    } catch (e) {
      // Don't emit error for class detail, just log it
      print('Failed to load class detail: $e');
    }
  }
}
