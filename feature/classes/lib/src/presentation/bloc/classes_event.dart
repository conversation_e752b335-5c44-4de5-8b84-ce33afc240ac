import 'package:equatable/equatable.dart';
import '../../domain/models/class_filter.dart';

/// Events for the Classes BLoC
abstract class ClassesEvent extends Equatable {
  const ClassesEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load categories
class ClassesCategoriesRequested extends ClassesEvent {
  const ClassesCategoriesRequested();
}

/// Event to load classes by category
class ClassesByCategoryRequested extends ClassesEvent {
  const ClassesByCategoryRequested({
    required this.categoryId,
    this.filter,
    this.refresh = false,
  });

  final String categoryId;
  final ClassFilter? filter;
  final bool refresh;

  @override
  List<Object?> get props => [categoryId, filter, refresh];
}

/// Event to load all classes
class ClassesAllRequested extends ClassesEvent {
  const ClassesAllRequested({
    this.filter,
    this.refresh = false,
  });

  final ClassFilter? filter;
  final bool refresh;

  @override
  List<Object?> get props => [filter, refresh];
}

/// Event to search classes
class ClassesSearchRequested extends ClassesEvent {
  const ClassesSearchRequested({
    required this.query,
    this.filter,
  });

  final String query;
  final ClassFilter? filter;

  @override
  List<Object?> get props => [query, filter];
}

/// Event to clear search
class ClassesSearchCleared extends ClassesEvent {
  const ClassesSearchCleared();
}

/// Event to change category
class ClassesCategoryChanged extends ClassesEvent {
  const ClassesCategoryChanged({
    required this.categoryId,
  });

  final String categoryId;

  @override
  List<Object?> get props => [categoryId];
}

/// Event to apply filter
class ClassesFilterApplied extends ClassesEvent {
  const ClassesFilterApplied({
    required this.filter,
  });

  final ClassFilter filter;

  @override
  List<Object?> get props => [filter];
}

/// Event to clear filter
class ClassesFilterCleared extends ClassesEvent {
  const ClassesFilterCleared();
}

/// Event to toggle bookmark
class ClassesBookmarkToggled extends ClassesEvent {
  const ClassesBookmarkToggled({
    required this.classId,
    required this.isBookmarked,
  });

  final String classId;
  final bool isBookmarked;

  @override
  List<Object?> get props => [classId, isBookmarked];
}

/// Event to rate a class
class ClassesRatingSubmitted extends ClassesEvent {
  const ClassesRatingSubmitted({
    required this.classId,
    required this.rating,
  });

  final String classId;
  final double rating;

  @override
  List<Object?> get props => [classId, rating];
}

/// Event to load more classes (pagination)
class ClassesLoadMoreRequested extends ClassesEvent {
  const ClassesLoadMoreRequested();
}

/// Event to refresh classes
class ClassesRefreshRequested extends ClassesEvent {
  const ClassesRefreshRequested();
}

/// Event to load featured classes
class ClassesFeaturedRequested extends ClassesEvent {
  const ClassesFeaturedRequested({
    this.limit,
  });

  final int? limit;

  @override
  List<Object?> get props => [limit];
}

/// Event to load class details
class ClassesDetailRequested extends ClassesEvent {
  const ClassesDetailRequested({
    required this.classId,
  });

  final String classId;

  @override
  List<Object?> get props => [classId];
}
