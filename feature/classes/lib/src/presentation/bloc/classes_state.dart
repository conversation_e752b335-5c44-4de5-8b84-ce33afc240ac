import 'package:equatable/equatable.dart';
import '../../domain/models/fitness_class.dart';
import '../../domain/models/class_category.dart';
import '../../domain/models/class_filter.dart';

/// States for the Classes BLoC
abstract class ClassesState extends Equatable {
  const ClassesState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class ClassesInitial extends ClassesState {
  const ClassesInitial();
}

/// Loading state
class ClassesLoading extends ClassesState {
  const ClassesLoading();
}

/// Loading more state (for pagination)
class ClassesLoadingMore extends ClassesState {
  const ClassesLoadingMore({
    required this.currentClasses,
    required this.categories,
    required this.selectedCategoryId,
    required this.currentFilter,
    required this.searchQuery,
  });

  final List<FitnessClass> currentClasses;
  final List<ClassCategory> categories;
  final String? selectedCategoryId;
  final ClassFilter currentFilter;
  final String? searchQuery;

  @override
  List<Object?> get props => [
        currentClasses,
        categories,
        selectedCategoryId,
        currentFilter,
        searchQuery,
      ];
}

/// Success state with data
class ClassesLoaded extends ClassesState {
  const ClassesLoaded({
    required this.classes,
    required this.categories,
    required this.selectedCategoryId,
    required this.currentFilter,
    required this.searchQuery,
    required this.hasMoreData,
    required this.totalCount,
    this.featuredClasses = const [],
    this.selectedClass,
  });

  final List<FitnessClass> classes;
  final List<ClassCategory> categories;
  final String? selectedCategoryId;
  final ClassFilter currentFilter;
  final String? searchQuery;
  final bool hasMoreData;
  final int totalCount;
  final List<FitnessClass> featuredClasses;
  final FitnessClass? selectedClass;

  /// Get the selected category object
  ClassCategory? get selectedCategory {
    if (selectedCategoryId == null) return null;
    try {
      return categories.firstWhere((cat) => cat.id == selectedCategoryId);
    } catch (e) {
      return null;
    }
  }

  /// Check if we're in search mode
  bool get isSearchMode => searchQuery?.isNotEmpty == true;

  /// Check if any filters are applied
  bool get hasActiveFilters => currentFilter.hasActiveFilters;

  /// Get display title based on current state
  String get displayTitle {
    if (isSearchMode) {
      return 'Search Results';
    } else if (selectedCategory != null) {
      return selectedCategory!.name;
    } else {
      return 'Browse By';
    }
  }

  /// Get subtitle based on current state
  String get displaySubtitle {
    if (isSearchMode) {
      return '${classes.length} results for "${searchQuery}"';
    } else if (selectedCategory != null) {
      return '${classes.length} workouts';
    } else {
      return '${totalCount} workouts';
    }
  }

  /// Copy with method for state updates
  ClassesLoaded copyWith({
    List<FitnessClass>? classes,
    List<ClassCategory>? categories,
    String? selectedCategoryId,
    ClassFilter? currentFilter,
    String? searchQuery,
    bool? hasMoreData,
    int? totalCount,
    List<FitnessClass>? featuredClasses,
    FitnessClass? selectedClass,
    bool clearSelectedClass = false,
    bool clearSearchQuery = false,
  }) {
    return ClassesLoaded(
      classes: classes ?? this.classes,
      categories: categories ?? this.categories,
      selectedCategoryId: selectedCategoryId ?? this.selectedCategoryId,
      currentFilter: currentFilter ?? this.currentFilter,
      searchQuery: clearSearchQuery ? null : (searchQuery ?? this.searchQuery),
      hasMoreData: hasMoreData ?? this.hasMoreData,
      totalCount: totalCount ?? this.totalCount,
      featuredClasses: featuredClasses ?? this.featuredClasses,
      selectedClass: clearSelectedClass ? null : (selectedClass ?? this.selectedClass),
    );
  }

  @override
  List<Object?> get props => [
        classes,
        categories,
        selectedCategoryId,
        currentFilter,
        searchQuery,
        hasMoreData,
        totalCount,
        featuredClasses,
        selectedClass,
      ];
}

/// Error state
class ClassesError extends ClassesState {
  const ClassesError({
    required this.message,
    this.previousState,
  });

  final String message;
  final ClassesState? previousState;

  @override
  List<Object?> get props => [message, previousState];
}

/// Bookmark operation states
class ClassesBookmarkLoading extends ClassesState {
  const ClassesBookmarkLoading({
    required this.classId,
    required this.currentState,
  });

  final String classId;
  final ClassesLoaded currentState;

  @override
  List<Object?> get props => [classId, currentState];
}

class ClassesBookmarkSuccess extends ClassesState {
  const ClassesBookmarkSuccess({
    required this.classId,
    required this.isBookmarked,
    required this.updatedState,
  });

  final String classId;
  final bool isBookmarked;
  final ClassesLoaded updatedState;

  @override
  List<Object?> get props => [classId, isBookmarked, updatedState];
}

class ClassesBookmarkError extends ClassesState {
  const ClassesBookmarkError({
    required this.message,
    required this.classId,
    required this.previousState,
  });

  final String message;
  final String classId;
  final ClassesLoaded previousState;

  @override
  List<Object?> get props => [message, classId, previousState];
}

/// Rating operation states
class ClassesRatingLoading extends ClassesState {
  const ClassesRatingLoading({
    required this.classId,
    required this.currentState,
  });

  final String classId;
  final ClassesLoaded currentState;

  @override
  List<Object?> get props => [classId, currentState];
}

class ClassesRatingSuccess extends ClassesState {
  const ClassesRatingSuccess({
    required this.classId,
    required this.rating,
    required this.updatedState,
  });

  final String classId;
  final double rating;
  final ClassesLoaded updatedState;

  @override
  List<Object?> get props => [classId, rating, updatedState];
}

class ClassesRatingError extends ClassesState {
  const ClassesRatingError({
    required this.message,
    required this.classId,
    required this.previousState,
  });

  final String message;
  final String classId;
  final ClassesLoaded previousState;

  @override
  List<Object?> get props => [message, classId, previousState];
}
