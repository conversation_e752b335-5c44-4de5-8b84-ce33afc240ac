import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'domain/repositories/classes_repository.dart';
import 'data/repositories/classes_repository_impl.dart';
import 'data/datasources/classes_api_datasource.dart';
import 'data/datasources/classes_mock_datasource.dart';
import 'presentation/bloc/classes_bloc.dart';

/// Dependency injection for classes feature
class ClassesInjection {
  static ClassesInjection? _instance;
  static ClassesInjection get instance => _instance ??= ClassesInjection._();
  ClassesInjection._();

  // Configuration - USING REAL API DATA
  static bool _useMockData = false; // Using real API instead of mock data
  static String _baseUrl = 'http://10.0.2.2:3000'; // API base URL
  static String _apiKey = 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b'; // API key

  /// Configure data source
  static void configureMockData(bool useMock) {
    _useMockData = useMock;
  }

  /// Configure API base URL
  static void configureBaseUrl(String baseUrl) {
    _baseUrl = baseUrl;
  }

  /// Configure API key
  static void configureApiKey(String apiKey) {
    _apiKey = apiKey;
  }

  /// Get classes repository
  static ClassesRepository getRepository({
    http.Client? httpClient,
    String? baseUrl,
    String? apiKey,
  }) {
    final apiDataSource = ClassesApiDataSource(
      baseUrl: baseUrl ?? _baseUrl,
      httpClient: httpClient,
      apiKey: apiKey ?? _apiKey,
    );
    
    final mockDataSource = ClassesMockDataSource();
    
    return ClassesRepositoryImpl(
      apiDataSource: apiDataSource,
      mockDataSource: mockDataSource,
      useMockData: _useMockData,
    );
  }

  /// Get classes BLoC
  static ClassesBloc getBloc({
    http.Client? httpClient,
    String? baseUrl,
    String? apiKey,
  }) {
    return ClassesBloc(
      repository: getRepository(
        httpClient: httpClient,
        baseUrl: baseUrl,
        apiKey: apiKey,
      ),
    );
  }

  /// Provide BLoC for widget tree
  static BlocProvider<ClassesBloc> provideBlocToWidget({
    required Widget child,
    http.Client? httpClient,
    String? baseUrl,
    String? apiKey,
  }) {
    return BlocProvider<ClassesBloc>(
      create: (context) => getBloc(
        httpClient: httpClient,
        baseUrl: baseUrl,
        apiKey: apiKey,
      ),
      child: child,
    );
  }

  /// Dispose resources
  static void dispose() {
    // Clean up any resources if needed
  }
}

/// Extension for easy BLoC access
extension ClassesBlocExtension on BuildContext {
  ClassesBloc get classesBloc => read<ClassesBloc>();
}
