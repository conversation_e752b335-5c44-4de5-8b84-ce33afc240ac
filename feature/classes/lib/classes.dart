/// Classes feature module
/// 
/// This module provides functionality for browsing and filtering fitness classes
/// with categories like Abs & Core, Arms & Shoulders, Glutes & Legs, etc.
library classes;

// Domain Models
export 'src/domain/models/fitness_class.dart';
export 'src/domain/models/class_category.dart';
export 'src/domain/models/class_filter.dart';

// Domain Repositories
export 'src/domain/repositories/classes_repository.dart';

// Data Layer
export 'src/data/repositories/classes_repository_impl.dart';
export 'src/data/datasources/classes_api_datasource.dart';
export 'src/data/datasources/classes_mock_datasource.dart';

// Presentation Layer
export 'src/presentation/bloc/classes_bloc.dart';
export 'src/presentation/bloc/classes_event.dart';
export 'src/presentation/bloc/classes_state.dart';
export 'src/presentation/widgets/classes_screen.dart';
export 'src/presentation/widgets/class_card.dart';
export 'src/presentation/widgets/category_tabs.dart';
export 'src/presentation/widgets/filter_bottom_sheet.dart';

// Dependency Injection
export 'src/injection.dart';
