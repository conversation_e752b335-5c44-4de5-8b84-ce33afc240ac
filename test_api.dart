import 'dart:convert';
import 'dart:io';

/// Simple script to test API connection for Classes feature
/// Run with: dart test_api.dart
void main() async {
  const baseUrl = 'http://********:3000';
  const apiKey = 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b';
  
  print('🧪 Testing Classes API Connection...');
  print('📍 Base URL: $baseUrl');
  print('🔑 API Key: ${apiKey.substring(0, 20)}...');
  print('');
  
  final client = HttpClient();
  
  try {
    // Test 1: Categories endpoint
    await testEndpoint(
      client,
      '$baseUrl/api/public/v1/classes/categories',
      'Categories',
      apiKey,
    );
    
    // Test 2: Classes endpoint
    await testEndpoint(
      client,
      '$baseUrl/api/public/v1/classes',
      'Classes',
      apiKey,
    );
    
    print('');
    print('✅ API tests completed!');
    
  } catch (e) {
    print('❌ Test failed: $e');
  } finally {
    client.close();
  }
}

Future<void> testEndpoint(
  HttpClient client,
  String url,
  String name,
  String apiKey,
) async {
  print('🔍 Testing $name endpoint...');
  print('   URL: $url');
  
  try {
    final uri = Uri.parse(url);
    final request = await client.getUrl(uri);
    
    // Add headers
    request.headers.set('Content-Type', 'application/json');
    request.headers.set('x-api-key', apiKey);
    
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    print('   Status: ${response.statusCode}');
    print('   Headers: ${response.headers}');
    
    if (response.statusCode == 200) {
      print('   ✅ Success!');
      
      try {
        final data = json.decode(responseBody);
        print('   📄 Response type: ${data.runtimeType}');
        
        if (data is Map<String, dynamic>) {
          print('   📊 Response keys: ${data.keys.toList()}');
          
          if (data.containsKey('success')) {
            print('   🎯 Success field: ${data['success']}');
          }
          
          if (data.containsKey('data')) {
            final dataField = data['data'];
            print('   📦 Data type: ${dataField.runtimeType}');
            
            if (dataField is Map<String, dynamic>) {
              print('   📋 Data keys: ${dataField.keys.toList()}');
            } else if (dataField is List) {
              print('   📝 Data length: ${dataField.length}');
            }
          }
        } else if (data is List) {
          print('   📝 Response length: ${data.length}');
          if (data.isNotEmpty) {
            print('   🔍 First item type: ${data.first.runtimeType}');
            if (data.first is Map<String, dynamic>) {
              print('   🗝️ First item keys: ${(data.first as Map<String, dynamic>).keys.toList()}');
            }
          }
        }
        
        // Show sample response (truncated)
        final sampleResponse = responseBody.length > 200 
            ? '${responseBody.substring(0, 200)}...' 
            : responseBody;
        print('   📄 Sample response: $sampleResponse');
        
      } catch (e) {
        print('   ⚠️ Could not parse JSON: $e');
        print('   📄 Raw response: ${responseBody.substring(0, 100)}...');
      }
    } else {
      print('   ❌ Failed with status ${response.statusCode}');
      print('   📄 Response: ${responseBody.substring(0, 200)}');
      
      if (response.statusCode == 401) {
        print('   💡 Hint: Check API key configuration');
      } else if (response.statusCode == 404) {
        print('   💡 Hint: Check if API server is running and endpoint exists');
      } else if (response.statusCode >= 500) {
        print('   💡 Hint: Server error - check API server logs');
      }
    }
    
  } on SocketException catch (e) {
    print('   ❌ Connection failed: $e');
    print('   💡 Hint: Make sure API server is running at $url');
  } catch (e) {
    print('   ❌ Request failed: $e');
  }
  
  print('');
}
