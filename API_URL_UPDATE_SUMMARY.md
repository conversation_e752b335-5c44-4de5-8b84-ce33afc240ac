# API URL Update: localhost:3000 → ********:3000

## 🔄 **Change Summary**
Updated all API calls from `localhost:3000` to `********:3000` untuk Android emulator compatibility.

## 🎯 **Why This Change?**

### **Android Emulator Network Configuration**
- ✅ ************: Special IP address yang digunakan Android emulator untuk mengakses `localhost` dari host machine
- ❌ **localhost**: Tidak bisa diakses dari dalam Android emulator karena merujuk ke emulator itu sendiri
- ✅ **Cross-Platform**: Tetap kompatibel dengan iOS simulator dan real devices

### **Network Mapping**
```
Host Machine (localhost:3000) → Android Emulator (********:3000)
```

## 📁 **Files Updated**

### **1. Dependency Injection**
**File:** `feature/schedules/lib/src/injection.dart`
```dart
// Before
final restClient = RestClientHttp(baseUrl: 'http://localhost:3000');

// After  
final restClient = RestClientHttp(baseUrl: 'http://********:3000');
```

### **2. UI Error Messages**
**File:** `feature/schedules/lib/src/presentation/widgets/schedules_screen.dart`
```dart
// Before
errorMessage ?? 'Unable to connect to http://localhost:3000/api/public/class-schedules'

// After
errorMessage ?? 'Unable to connect to http://********:3000/api/public/class-schedules'
```

### **3. Unit Tests**
**File:** `feature/schedules/test/api_date_test.dart`
```dart
// Before
expect(fullUrl, equals('http://localhost:3000/api/public/class-schedules?tenantId=1&startDate=2025-07-09&endDate=2025-07-16'));

// After
expect(fullUrl, equals('http://********:3000/api/public/class-schedules?tenantId=1&startDate=2025-07-09&endDate=2025-07-16'));
```

### **4. Documentation**
**File:** `API_DATE_FILTERING_FIX.md`
- Updated all example URLs dari `localhost:3000` ke `********:3000`

## 🌐 **Updated API Endpoints**

### **Today's Schedules**
```http
GET http://********:3000/api/public/class-schedules?tenantId=1&startDate=2025-07-26&endDate=2025-07-26
```

### **Date Range Filtering**
```http
GET http://********:3000/api/public/class-schedules?tenantId=1&startDate=2025-07-09&endDate=2025-07-16
```

### **Specific Date Selection**
```http
GET http://********:3000/api/public/class-schedules?tenantId=1&startDate=2025-07-15&endDate=2025-07-15
```

### **Upcoming Schedules**
```http
GET http://********:3000/api/public/class-schedules?tenantId=1&startDate=2025-07-26
```

### **Search Schedules**
```http
GET http://********:3000/api/public/class-schedules/search?tenantId=1&q=yoga
```

### **Book Schedule**
```http
POST http://********:3000/api/public/class-schedules/123/book
```

### **Cancel Booking**
```http
DELETE http://********:3000/api/public/class-schedules/123/book?tenantId=1
```

## 🧪 **Testing Verification**

### **Unit Tests Status**
- ✅ **All tests pass**: Date formatting tests updated dan berhasil
- ✅ **URL generation**: Test URL generation dengan IP baru berhasil
- ✅ **API call format**: Semua format API call sudah benar

### **Build Verification**
- ✅ **Flutter analyze**: No errors (hanya flutter_lints warning)
- ✅ **App build**: Successfully compiles APK
- ✅ **Type safety**: All API calls properly typed

## 📱 **Platform Compatibility**

### **Android Emulator**
- ✅ **********:3000**: Dapat mengakses server di host machine
- ✅ **Network connectivity**: Emulator dapat connect ke API server

### **iOS Simulator**
- ✅ **localhost:3000**: iOS simulator masih bisa menggunakan localhost
- ⚠️ **Note**: Jika perlu, bisa menggunakan IP address host machine

### **Real Devices**
- ✅ **WiFi network**: Real devices perlu menggunakan IP address host machine di network yang sama
- 📝 **Example**: `http://*************:3000` (sesuai IP host machine)

## 🔧 **Development Setup**

### **Server Requirements**
1. **API Server**: Harus running di `localhost:3000` pada host machine
2. **Network Access**: Server harus accessible dari emulator
3. **CORS Configuration**: Pastikan server mengizinkan requests dari emulator

### **Testing on Different Platforms**
```dart
// Untuk production, bisa menggunakan environment-based configuration
final baseUrl = Platform.isAndroid 
    ? 'http://********:3000'  // Android emulator
    : 'http://localhost:3000'; // iOS simulator
```

## 🎯 **Expected Behavior**

### **Android Emulator**
- ✅ **API Calls**: Berhasil connect ke server di host machine
- ✅ **Date Filtering**: Calendar date selection works correctly
- ✅ **Error Handling**: Clear error messages dengan IP yang benar

### **Development Workflow**
1. **Start API Server**: `npm start` di host machine (localhost:3000)
2. **Run Flutter App**: `flutter run` untuk Android emulator
3. **API Connectivity**: App akan connect ke `********:3000`
4. **Testing**: Semua schedules functionality works dengan real API

## 🚀 **Production Considerations**

### **Environment Configuration**
Untuk production, pertimbangkan menggunakan:
```dart
class ApiConfig {
  static const String baseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'http://********:3000', // Development default
  );
}
```

### **Network Security**
- ✅ **HTTPS**: Gunakan HTTPS untuk production
- ✅ **Authentication**: Implement proper API authentication
- ✅ **Error Handling**: Comprehensive error handling untuk network issues

## ✅ **Verification Complete**

- ✅ **All files updated**: localhost:3000 → ********:3000
- ✅ **Tests passing**: Unit tests dengan URL baru berhasil
- ✅ **Build successful**: App compiles tanpa error
- ✅ **Android compatibility**: Ready untuk testing di Android emulator

**API URL update completed successfully!** 🎉
